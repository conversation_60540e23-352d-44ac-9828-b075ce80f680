package dlt69845

import (
	"tp.service/internal/protocol/axdr"
)

type Bool axdr.Boolean
type BitString axdr.BitString
type DoubleLong axdr.Integer
type DoubleLongUnsigned axdr.Integer
type OctetString axdr.ByteString
type VisibleString axdr.VisibleString
type UTF8String axdr.VisibleString
type Integer axdr.Integer
type Long axdr.Integer
type Unsigned axdr.Integer
type LongUnsigned axdr.Integer
type Long64 axdr.Integer

// type Long64Unsigned axdr.Integer
type Enum axdr.Enumerated
type Float32 axdr.Integer
type Float64 axdr.Integer

// --------------------------------- bool ------------------------------
func NewBool(vale bool) *Bool {
	return &Bool{}
}

func (v *Bool) Encode() ([]byte, error) {
	return (*axdr.Boolean)(v).Encode()
}

func (v *Bool) Decode(data []byte) (int, error) {
	return (*axdr.Bo<PERSON>an)(v).Decode(data)
}

func (v *Bool) Type() axdr.AXDRType {
	return axdr.TypeBoolean
}

func (v *Bool) String() string {
	return (*axdr.Boolean)(v).String()
}

// --------------------------------- bit-string ------------------------------
func NewBitString(bits []bool) *BitString {
	byteLength := (len(bits) + 7) / 8
	value := make([]byte, byteLength)

	for i, bit := range bits {
		if bit {
			byteIndex := i / 8
			bitIndex := 7 - (i % 8) // 大端序位排列
			value[byteIndex] |= 1 << bitIndex
		}
	}
	return &BitString{
		Value:         value,
		BitLength:     len(bits),
		IsConstrained: false,
	}
}

func (v *BitString) Encode() ([]byte, error) {
	return (*axdr.BitString)(v).Encode()
}

func (v *BitString) Decode(data []byte) (int, error) {
	return (*axdr.BitString)(v).Decode(data)
}

func (v *BitString) Type() axdr.AXDRType {
	return axdr.TypeBitString
}

func (v *BitString) String() string {
	return (*axdr.BitString)(v).String()
}

// --------------------------------- double-long ------------------------------
func NewDoubleLong(value int32) *DoubleLong {
	return &DoubleLong{
		Value:         int64(value),
		IsConstrained: true,
		MinValue:      -2147483648,
		MaxValue:      2147483647,
		ByteLength:    4,
	}
}
func (v *DoubleLong) Encode() ([]byte, error) {
	return (*axdr.Integer)(v).Encode()
}

func (v *DoubleLong) Decode(data []byte) (int, error) {
	return (*axdr.Integer)(v).Decode(data)
}

func (v *DoubleLong) Type() axdr.AXDRType {
	return axdr.TypeInteger
}

func (v *DoubleLong) String() string {
	return (*axdr.Integer)(v).String()
}

// --------------------------------- double-long-unsigned ------------------------------
func NewDoubleLongUnsigned(value uint32) *DoubleLongUnsigned {
	return &DoubleLongUnsigned{
		Value:         int64(value),
		IsConstrained: true,
		MinValue:      0,
		MaxValue:      4294967295,
		ByteLength:    4,
	}
}
func (v *DoubleLongUnsigned) Encode() ([]byte, error) {
	return (*axdr.Integer)(v).Encode()
}

func (v *DoubleLongUnsigned) Decode(data []byte) (int, error) {
	return (*axdr.Integer)(v).Decode(data)
}

func (v *DoubleLongUnsigned) Type() axdr.AXDRType {
	return axdr.TypeInteger
}

func (v *DoubleLongUnsigned) String() string {
	return (*axdr.Integer)(v).String()
}

// --------------------------------- octet-string ------------------------------
func NewOctetString(value []byte) *OctetString {
	return &OctetString{
		Value: value,
	}
}

func (v *OctetString) Encode() ([]byte, error) {
	return (*axdr.ByteString)(v).Encode()
}

func (v *OctetString) Decode(data []byte) (int, error) {
	return (*axdr.ByteString)(v).Decode(data)
}

func (v *OctetString) Type() axdr.AXDRType {
	return axdr.TypeByteString
}

func (v *OctetString) String() string {
	return (*axdr.ByteString)(v).String()
}

// --------------------------------- visible-string ------------------------------
func NewVisibleString(value string) *VisibleString {
	return &VisibleString{
		Value: value,
	}
}

func (v *VisibleString) Encode() ([]byte, error) {
	return (*axdr.VisibleString)(v).Encode()
}

func (v *VisibleString) Decode(data []byte) (int, error) {
	return (*axdr.VisibleString)(v).Decode(data)
}

func (v *VisibleString) Type() axdr.AXDRType {
	return axdr.TypeVisibleString
}

func (v *VisibleString) String() string {
	return (*axdr.VisibleString)(v).String()
}

// --------------------------------- UTF8-string ------------------------------
func NewUTF8String(value string) *UTF8String {
	return &UTF8String{
		Value: value,
	}
}

func (v *UTF8String) Encode() ([]byte, error) {
	return (*axdr.VisibleString)(v).Encode()
}

func (v *UTF8String) Decode(data []byte) (int, error) {
	return (*axdr.VisibleString)(v).Decode(data)
}

func (v *UTF8String) Type() axdr.AXDRType {
	return axdr.TypeVisibleString
}

func (v *UTF8String) String() string {
	return (*axdr.VisibleString)(v).String()
}

// --------------------------------- integer ------------------------------
func NewInteger(value int8) *Integer {
	return &Integer{
		Value:         int64(value),
		IsConstrained: true,
		MinValue:      -128,
		MaxValue:      127,
		ByteLength:    1,
	}
}

func (v *Integer) Encode() ([]byte, error) {
	return (*axdr.Integer)(v).Encode()
}

func (v *Integer) Decode(data []byte) (int, error) {
	return (*axdr.Integer)(v).Decode(data)
}

func (v *Integer) Type() axdr.AXDRType {
	return axdr.TypeInteger
}

func (v *Integer) String() string {
	return (*axdr.Integer)(v).String()
}

// --------------------------------- long ------------------------------
func NewLong(value int16) *Long {
	return &Long{
		Value:         int64(value),
		IsConstrained: true,
		MinValue:      -32768,
		MaxValue:      32767,
		ByteLength:    2,
	}
}

func (v *Long) Encode() ([]byte, error) {
	return (*axdr.Integer)(v).Encode()
}

func (v *Long) Decode(data []byte) (int, error) {
	return (*axdr.Integer)(v).Decode(data)
}

func (v *Long) Type() axdr.AXDRType {
	return axdr.TypeInteger
}

func (v *Long) String() string {
	return (*axdr.Integer)(v).String()
}

// --------------------------------- unsigned ------------------------------
func NewUnsigned(value uint8) *Unsigned {
	return &Unsigned{
		Value:         int64(value),
		IsConstrained: true,
		MinValue:      0,
		MaxValue:      255,
		ByteLength:    1,
	}
}

func (v *Unsigned) Encode() ([]byte, error) {
	return (*axdr.Integer)(v).Encode()
}

func (v *Unsigned) Decode(data []byte) (int, error) {
	return (*axdr.Integer)(v).Decode(data)
}

func (v *Unsigned) Type() axdr.AXDRType {
	return axdr.TypeInteger
}

func (v *Unsigned) String() string {
	return (*axdr.Integer)(v).String()
}

// --------------------------------- long-unsigned ------------------------------
func NewLongUnsigned(value uint16) *LongUnsigned {
	return &LongUnsigned{
		Value:         int64(value),
		IsConstrained: true,
		MinValue:      0,
		MaxValue:      65535,
		ByteLength:    2,
	}
}

func (v *LongUnsigned) Encode() ([]byte, error) {
	return (*axdr.Integer)(v).Encode()
}

func (v *LongUnsigned) Decode(data []byte) (int, error) {
	return (*axdr.Integer)(v).Decode(data)
}

func (v *LongUnsigned) Type() axdr.AXDRType {
	return axdr.TypeInteger
}

func (v *LongUnsigned) String() string {
	return (*axdr.Integer)(v).String()
}

// --------------------------------- long64 ------------------------------
func NewLong64(value int64) *Long64 {
	return &Long64{
		Value:         value,
		IsConstrained: true,
		MinValue:      -9223372036854775808,
		MaxValue:      9223372036854775807,
		ByteLength:    8,
	}
}

func (v *Long64) Encode() ([]byte, error) {
	return (*axdr.Integer)(v).Encode()
}

func (v *Long64) Decode(data []byte) (int, error) {
	return (*axdr.Integer)(v).Decode(data)
}

func (v *Long64) Type() axdr.AXDRType {
	return axdr.TypeInteger
}

func (v *Long64) String() string {
	return (*axdr.Integer)(v).String()
}

// --------------------------------- long64-unsigned ------------------------------
// func NewLong64Unsigned(value uint64) *Long64Unsigned {
// 	return &Long64Unsigned{
// 		Value:         int64(value),
// 		IsConstrained: true,
// 		MinValue:      0,
// 		MaxValue:      0xFFFFFFFFFFFFFFFF,
// 		ByteLength:    8,
// 	}
// }
//
// func (v *Long64Unsigned) Encode() ([]byte, error) {
// 	return (*axdr.Integer)(v).Encode()
// }
//
// func (v *Long64Unsigned) Decode(data []byte) (int, error) {
// 	return (*axdr.Integer)(v).Decode(data)
// }
//
// func (v *Long64Unsigned) Type() axdr.AXDRType {
// 	return axdr.TypeInteger
// }

// func (v *Long64Unsigned) String() string {
// 	return (*axdr.Integer)(v).String()
// }

// --------------------------------- enum ------------------------------
func NewEnum(value uint8) *Enum {
	return &Enum{
		Value: byte(value),
	}
}

func (v *Enum) Encode() ([]byte, error) {
	return (*axdr.Enumerated)(v).Encode()
}

func (v *Enum) Decode(data []byte) (int, error) {
	return (*axdr.Enumerated)(v).Decode(data)
}

func (v *Enum) Type() axdr.AXDRType {
	return axdr.TypeEnumerated
}

func (v *Enum) String() string {
	return (*axdr.Enumerated)(v).String()
}
