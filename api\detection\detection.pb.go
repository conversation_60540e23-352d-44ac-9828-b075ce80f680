//*
// 检测服务网关
// 根据请求中的检测服务名称重定向到指定服务

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/detection/detection.proto

package detection

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 检测过程消息类型
type MessageType int32

const (
	// 检测项开始
	MessageType_START MessageType = 0
	// 检测项进度
	MessageType_PROGRESS MessageType = 1
	// 检测项过程消息
	MessageType_MESSAGE MessageType = 2
	// 检测项成功消息
	MessageType_SUCCESS MessageType = 3
	// 检测项失败消息
	MessageType_FAILURE MessageType = 4
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "START",
		1: "PROGRESS",
		2: "MESSAGE",
		3: "SUCCESS",
		4: "FAILURE",
	}
	MessageType_value = map[string]int32{
		"START":    0,
		"PROGRESS": 1,
		"MESSAGE":  2,
		"SUCCESS":  3,
		"FAILURE":  4,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_detection_detection_proto_enumTypes[0].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_api_detection_detection_proto_enumTypes[0]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{0}
}

// 服务基本信息
type ServiceInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务名称
	// 每个检测服务需求分析时确定，不可重复
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 服务版本
	// 用于服务升级时的兼容性判断
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// 服务描述
	// 用于展示给用户，描述服务的功能、用途等信息
	Description   string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceInfo) Reset() {
	*x = ServiceInfo{}
	mi := &file_api_detection_detection_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfo) ProtoMessage() {}

func (x *ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfo.ProtoReflect.Descriptor instead.
func (*ServiceInfo) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ServiceInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 检测服务的检测项
type DetectionItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测项ID
	// 每个检测服务需求分析时确定，不可重复
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 检测项名称
	// 用于展示给用户，描述检测项内容、功能等信息
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 检测项描述
	// 用于展示给用户，描述检测项的执行过程、结果等信息
	Description   string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionItem) Reset() {
	*x = DetectionItem{}
	mi := &file_api_detection_detection_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionItem) ProtoMessage() {}

func (x *DetectionItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionItem.ProtoReflect.Descriptor instead.
func (*DetectionItem) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{1}
}

func (x *DetectionItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DetectionItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetectionItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 服务信息获取请求
type GetServiceInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测服务名称
	// PVIIDPU -> 光伏 II 型分布式电源接入单元检测服务
	// SEU -> 智慧能源单元检测服务
	// ...
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoRequest) Reset() {
	*x = GetServiceInfoRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoRequest) ProtoMessage() {}

func (x *GetServiceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{2}
}

func (x *GetServiceInfoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 服务信息获取响应
type GetServiceInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []*ServiceInfo         `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoResponse) Reset() {
	*x = GetServiceInfoResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoResponse) ProtoMessage() {}

func (x *GetServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{3}
}

func (x *GetServiceInfoResponse) GetServices() []*ServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

// 检测初始化请求
type DetectionInitRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测服务名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 检测配置, JSON 格式
	// 检测服务需求分析时确定
	Config        string `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionInitRequest) Reset() {
	*x = DetectionInitRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionInitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionInitRequest) ProtoMessage() {}

func (x *DetectionInitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionInitRequest.ProtoReflect.Descriptor instead.
func (*DetectionInitRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{4}
}

func (x *DetectionInitRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetectionInitRequest) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

// 检测初始化响应
type DetectionInitResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测令牌，用于后续访问检测服务
	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionInitResponse) Reset() {
	*x = DetectionInitResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionInitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionInitResponse) ProtoMessage() {}

func (x *DetectionInitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionInitResponse.ProtoReflect.Descriptor instead.
func (*DetectionInitResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{5}
}

func (x *DetectionInitResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测清理请求
type DetectionCleanupRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测令牌
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 是否强制清理
	// 存在运行中的检测任务时，根据是否强制清理标志决定是否清理
	Force         bool `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionCleanupRequest) Reset() {
	*x = DetectionCleanupRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionCleanupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionCleanupRequest) ProtoMessage() {}

func (x *DetectionCleanupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionCleanupRequest.ProtoReflect.Descriptor instead.
func (*DetectionCleanupRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{6}
}

func (x *DetectionCleanupRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DetectionCleanupRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

// 检测清理响应
type DetectionCleanupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionCleanupResponse) Reset() {
	*x = DetectionCleanupResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionCleanupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionCleanupResponse) ProtoMessage() {}

func (x *DetectionCleanupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionCleanupResponse.ProtoReflect.Descriptor instead.
func (*DetectionCleanupResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{7}
}

// 检测项获取请求
type DetectionGetItemsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测令牌
	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionGetItemsRequest) Reset() {
	*x = DetectionGetItemsRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionGetItemsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionGetItemsRequest) ProtoMessage() {}

func (x *DetectionGetItemsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionGetItemsRequest.ProtoReflect.Descriptor instead.
func (*DetectionGetItemsRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{8}
}

func (x *DetectionGetItemsRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测项获取响应
type DetectionGetItemsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测项列表
	Items         []*DetectionItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionGetItemsResponse) Reset() {
	*x = DetectionGetItemsResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionGetItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionGetItemsResponse) ProtoMessage() {}

func (x *DetectionGetItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionGetItemsResponse.ProtoReflect.Descriptor instead.
func (*DetectionGetItemsResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{9}
}

func (x *DetectionGetItemsResponse) GetItems() []*DetectionItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// 检测启动请求
type DetectionStartRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测令牌
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// 检测项 ID 集
	ItemIds       []int32 `protobuf:"varint,2,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStartRequest) Reset() {
	*x = DetectionStartRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStartRequest) ProtoMessage() {}

func (x *DetectionStartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStartRequest.ProtoReflect.Descriptor instead.
func (*DetectionStartRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{10}
}

func (x *DetectionStartRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DetectionStartRequest) GetItemIds() []int32 {
	if x != nil {
		return x.ItemIds
	}
	return nil
}

// 检测启动响应（服务端流式响应）
type DetectionStartStreamResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测项 ID
	ItemId int32 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	// 消息类型
	Type MessageType `protobuf:"varint,2,opt,name=type,proto3,enum=detection.MessageType" json:"type,omitempty"`
	// 消息内容
	Message       string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStartStreamResponse) Reset() {
	*x = DetectionStartStreamResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStartStreamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStartStreamResponse) ProtoMessage() {}

func (x *DetectionStartStreamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStartStreamResponse.ProtoReflect.Descriptor instead.
func (*DetectionStartStreamResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{11}
}

func (x *DetectionStartStreamResponse) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *DetectionStartStreamResponse) GetType() MessageType {
	if x != nil {
		return x.Type
	}
	return MessageType_START
}

func (x *DetectionStartStreamResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 检测停止请求
type DetectionStopRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测令牌
	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStopRequest) Reset() {
	*x = DetectionStopRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStopRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStopRequest) ProtoMessage() {}

func (x *DetectionStopRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStopRequest.ProtoReflect.Descriptor instead.
func (*DetectionStopRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{12}
}

func (x *DetectionStopRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测停止响应
type DetectionStopResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStopResponse) Reset() {
	*x = DetectionStopResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStopResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStopResponse) ProtoMessage() {}

func (x *DetectionStopResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStopResponse.ProtoReflect.Descriptor instead.
func (*DetectionStopResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{13}
}

// 检测重新开始请求
type DetectionRestartRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 检测令牌
	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionRestartRequest) Reset() {
	*x = DetectionRestartRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionRestartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionRestartRequest) ProtoMessage() {}

func (x *DetectionRestartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionRestartRequest.ProtoReflect.Descriptor instead.
func (*DetectionRestartRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{14}
}

func (x *DetectionRestartRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测重新开始响应
type DetectionRestartResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionRestartResponse) Reset() {
	*x = DetectionRestartResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionRestartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionRestartResponse) ProtoMessage() {}

func (x *DetectionRestartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionRestartResponse.ProtoReflect.Descriptor instead.
func (*DetectionRestartResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{15}
}

var File_api_detection_detection_proto protoreflect.FileDescriptor

const file_api_detection_detection_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/detection/detection.proto\x12\tdetection\"]\n" +
	"\vServiceInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"U\n" +
	"\rDetectionItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"+\n" +
	"\x15GetServiceInfoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"L\n" +
	"\x16GetServiceInfoResponse\x122\n" +
	"\bservices\x18\x01 \x03(\v2\x16.detection.ServiceInfoR\bservices\"B\n" +
	"\x14DetectionInitRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06config\x18\x02 \x01(\tR\x06config\"-\n" +
	"\x15DetectionInitResponse\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"E\n" +
	"\x17DetectionCleanupRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x14\n" +
	"\x05force\x18\x02 \x01(\bR\x05force\"\x1a\n" +
	"\x18DetectionCleanupResponse\"0\n" +
	"\x18DetectionGetItemsRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"K\n" +
	"\x19DetectionGetItemsResponse\x12.\n" +
	"\x05items\x18\x01 \x03(\v2\x18.detection.DetectionItemR\x05items\"H\n" +
	"\x15DetectionStartRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x19\n" +
	"\bitem_ids\x18\x02 \x03(\x05R\aitemIds\"}\n" +
	"\x1cDetectionStartStreamResponse\x12\x17\n" +
	"\aitem_id\x18\x01 \x01(\x05R\x06itemId\x12*\n" +
	"\x04type\x18\x02 \x01(\x0e2\x16.detection.MessageTypeR\x04type\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\",\n" +
	"\x14DetectionStopRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\x17\n" +
	"\x15DetectionStopResponse\"/\n" +
	"\x17DetectionRestartRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\x1a\n" +
	"\x18DetectionRestartResponse*M\n" +
	"\vMessageType\x12\t\n" +
	"\x05START\x10\x00\x12\f\n" +
	"\bPROGRESS\x10\x01\x12\v\n" +
	"\aMESSAGE\x10\x02\x12\v\n" +
	"\aSUCCESS\x10\x03\x12\v\n" +
	"\aFAILURE\x10\x042\x94\x05\n" +
	"\x14BaseDetectionService\x12U\n" +
	"\x0eGetServiceInfo\x12 .detection.GetServiceInfoRequest\x1a!.detection.GetServiceInfoResponse\x12X\n" +
	"\x13DetectionInitialize\x12\x1f.detection.DetectionInitRequest\x1a .detection.DetectionInitResponse\x12[\n" +
	"\x10DetectionCleanup\x12\".detection.DetectionCleanupRequest\x1a#.detection.DetectionCleanupResponse\x12^\n" +
	"\x11DetectionGetItems\x12#.detection.DetectionGetItemsRequest\x1a$.detection.DetectionGetItemsResponse\x12]\n" +
	"\x0eDetectionStart\x12 .detection.DetectionStartRequest\x1a'.detection.DetectionStartStreamResponse0\x01\x12R\n" +
	"\rDetectionStop\x12\x1f.detection.DetectionStopRequest\x1a .detection.DetectionStopResponse\x12[\n" +
	"\x10DetectionRestart\x12\".detection.DetectionRestartRequest\x1a#.detection.DetectionRestartResponseB\rZ\v./detectionb\x06proto3"

var (
	file_api_detection_detection_proto_rawDescOnce sync.Once
	file_api_detection_detection_proto_rawDescData []byte
)

func file_api_detection_detection_proto_rawDescGZIP() []byte {
	file_api_detection_detection_proto_rawDescOnce.Do(func() {
		file_api_detection_detection_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_detection_detection_proto_rawDesc), len(file_api_detection_detection_proto_rawDesc)))
	})
	return file_api_detection_detection_proto_rawDescData
}

var file_api_detection_detection_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_detection_detection_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_api_detection_detection_proto_goTypes = []any{
	(MessageType)(0),                     // 0: detection.MessageType
	(*ServiceInfo)(nil),                  // 1: detection.ServiceInfo
	(*DetectionItem)(nil),                // 2: detection.DetectionItem
	(*GetServiceInfoRequest)(nil),        // 3: detection.GetServiceInfoRequest
	(*GetServiceInfoResponse)(nil),       // 4: detection.GetServiceInfoResponse
	(*DetectionInitRequest)(nil),         // 5: detection.DetectionInitRequest
	(*DetectionInitResponse)(nil),        // 6: detection.DetectionInitResponse
	(*DetectionCleanupRequest)(nil),      // 7: detection.DetectionCleanupRequest
	(*DetectionCleanupResponse)(nil),     // 8: detection.DetectionCleanupResponse
	(*DetectionGetItemsRequest)(nil),     // 9: detection.DetectionGetItemsRequest
	(*DetectionGetItemsResponse)(nil),    // 10: detection.DetectionGetItemsResponse
	(*DetectionStartRequest)(nil),        // 11: detection.DetectionStartRequest
	(*DetectionStartStreamResponse)(nil), // 12: detection.DetectionStartStreamResponse
	(*DetectionStopRequest)(nil),         // 13: detection.DetectionStopRequest
	(*DetectionStopResponse)(nil),        // 14: detection.DetectionStopResponse
	(*DetectionRestartRequest)(nil),      // 15: detection.DetectionRestartRequest
	(*DetectionRestartResponse)(nil),     // 16: detection.DetectionRestartResponse
}
var file_api_detection_detection_proto_depIdxs = []int32{
	1,  // 0: detection.GetServiceInfoResponse.services:type_name -> detection.ServiceInfo
	2,  // 1: detection.DetectionGetItemsResponse.items:type_name -> detection.DetectionItem
	0,  // 2: detection.DetectionStartStreamResponse.type:type_name -> detection.MessageType
	3,  // 3: detection.BaseDetectionService.GetServiceInfo:input_type -> detection.GetServiceInfoRequest
	5,  // 4: detection.BaseDetectionService.DetectionInitialize:input_type -> detection.DetectionInitRequest
	7,  // 5: detection.BaseDetectionService.DetectionCleanup:input_type -> detection.DetectionCleanupRequest
	9,  // 6: detection.BaseDetectionService.DetectionGetItems:input_type -> detection.DetectionGetItemsRequest
	11, // 7: detection.BaseDetectionService.DetectionStart:input_type -> detection.DetectionStartRequest
	13, // 8: detection.BaseDetectionService.DetectionStop:input_type -> detection.DetectionStopRequest
	15, // 9: detection.BaseDetectionService.DetectionRestart:input_type -> detection.DetectionRestartRequest
	4,  // 10: detection.BaseDetectionService.GetServiceInfo:output_type -> detection.GetServiceInfoResponse
	6,  // 11: detection.BaseDetectionService.DetectionInitialize:output_type -> detection.DetectionInitResponse
	8,  // 12: detection.BaseDetectionService.DetectionCleanup:output_type -> detection.DetectionCleanupResponse
	10, // 13: detection.BaseDetectionService.DetectionGetItems:output_type -> detection.DetectionGetItemsResponse
	12, // 14: detection.BaseDetectionService.DetectionStart:output_type -> detection.DetectionStartStreamResponse
	14, // 15: detection.BaseDetectionService.DetectionStop:output_type -> detection.DetectionStopResponse
	16, // 16: detection.BaseDetectionService.DetectionRestart:output_type -> detection.DetectionRestartResponse
	10, // [10:17] is the sub-list for method output_type
	3,  // [3:10] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_api_detection_detection_proto_init() }
func file_api_detection_detection_proto_init() {
	if File_api_detection_detection_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_detection_detection_proto_rawDesc), len(file_api_detection_detection_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_detection_detection_proto_goTypes,
		DependencyIndexes: file_api_detection_detection_proto_depIdxs,
		EnumInfos:         file_api_detection_detection_proto_enumTypes,
		MessageInfos:      file_api_detection_detection_proto_msgTypes,
	}.Build()
	File_api_detection_detection_proto = out.File
	file_api_detection_detection_proto_goTypes = nil
	file_api_detection_detection_proto_depIdxs = nil
}
