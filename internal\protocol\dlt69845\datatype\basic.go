package datatype

import "tp.service/internal/protocol/axdr"

var codec = axdr.NewCodec()

// decodeBool 解码布尔值类型
func (dt *Bool) Encode() (byte, error) {
	// 使用 axdr 中 boolean 的编码规则
	data := axdr.NewBoolean(true)
	res, err := codec.Encode(data)
	if err != nil {
		return 0, err
	}
	if len(res) != 1 {
		return 0, axdr.ErrInvalidData
	}
	return res[0], nil
}

func (dt *Bool) Decode(b byte) (bool, error) {
	// 使用 axdr 中 boolean 的解码规则
	data := axdr.NewBoolean(false)
	res, err := codec.Decode([]byte{b}, data)
	if err != nil {
		return false, err
	}
	return res != 0, err
}
