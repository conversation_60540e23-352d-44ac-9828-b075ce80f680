# 协议服务网关

协议服务网关是一个统一的协议处理服务，根据请求中的 `name` 参数将请求重定向到相应的具体协议服务。

## 功能特性

- **统一接口**: 提供统一的协议处理接口，支持多种通信协议
- **动态路由**: 根据协议名称自动路由到对应的协议服务
- **服务发现**: 自动发现和注册协议服务
- **健康检查**: 定期检查服务健康状态
- **故障转移**: 支持远程服务不可用时自动切换到本地服务
- **扩展性**: 支持动态添加和移除协议服务

## 支持的协议

- **DL/T 698.45**: 电力行业通信协议
- **Modbus**: 工业通信协议 (计划支持)
- **DL/T 645**: 电能表通信协议 (计划支持)
- **自定义协议**: 支持扩展自定义协议

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   客户端请求     │───▶│  协议服务网关     │───▶│  具体协议服务    │
│                │    │                  │    │                │
│ - name: 协议名   │    │ - 路由管理        │    │ - DL/T 698.45  │
│ - params: 参数  │    │ - 服务发现        │    │ - Modbus       │
│ - data: 数据    │    │ - 健康检查        │    │ - DL/T 645     │
└─────────────────┘    │ - 故障转移        │    │ - 自定义协议    │
                       └──────────────────┘    └─────────────────┘
```

## 快速开始

### 1. 创建协议服务器

```go
import (
    "gorm.io/gorm"
    "tp.service/service"
)

// 使用默认配置
server := service.NewProtocolServer(db)

// 或使用自定义配置
config := &service.GatewayConfig{
    Services: map[string]service.ServiceConfig{
        "DL/T 698.45": {
            Address:    "", // 空地址使用内置服务
            Enabled:    true,
            RetryCount: 3,
        },
        "Modbus": {
            Address:    "modbus-service:50052", // 有地址使用远程服务
            Enabled:    false,
            RetryCount: 3,
        },
    },
    HealthCheckInterval: 60 * time.Second,
    ConnectionTimeout:   10 * time.Second,
}
server := service.NewProtocolServerWithConfig(db, config)
```

### 2. 获取服务信息

```go
ctx := context.Background()

// 获取所有服务
resp, err := server.GetServiceInfo(ctx, &proto.GetServiceInfoRequest{Name: ""})

// 获取特定服务
resp, err := server.GetServiceInfo(ctx, &proto.GetServiceInfoRequest{Name: "DL/T 698.45"})
```

### 3. 协议数据处理

```go
// 协议数据域组织
params := `{"type": 256, "addr": "123456789012", "data": {"test": "value"}}`
resp, err := server.ProtocolDataDomainOrganization(ctx, &proto.CPDORequest{
    Name:   "DL/T 698.45",
    Params: params,
})

// 协议组织
resp, err := server.ProtocolOrganization(ctx, &proto.CPORequest{
    Name:   "DL/T 698.45", 
    Params: params,
})

// 协议解析
resp, err := server.ProtocolParsing(ctx, &proto.CPPRequest{
    Name:  "DL/T 698.45",
    Frame: "68123456789012681000010203040516",
})

// 协议数据域解析
resp, err := server.ProtocolDataDomainParsing(ctx, &proto.CPDRequest{
    Name:       "DL/T 698.45",
    Datadomain: "010203040516",
})
```

## 配置说明

### GatewayConfig

| 字段 | 类型 | 说明 |
|------|------|------|
| ServiceDiscovery | map[string]ServiceConfig | 服务发现配置 |
| HealthCheckInterval | time.Duration | 健康检查间隔 |
| ConnectionTimeout | time.Duration | 连接超时时间 |

### ServiceConfig

| 字段 | 类型 | 说明 |
|------|------|------|
| Address | string | 服务地址 |
| Enabled | bool | 是否启用 |
| Type | string | 服务类型 (remote/local) |
| RetryCount | int | 重试次数 |

## 扩展自定义协议

### 1. 实现 ProtocolServiceClient 接口

```go
type CustomProtocolClient struct {
    // 自定义字段
}

func (c *CustomProtocolClient) GetServiceInfo(ctx context.Context) (*proto.ProtServiceInfo, error) {
    return &proto.ProtServiceInfo{
        Name:        "Custom Protocol",
        Version:     "1.0.0", 
        Description: "自定义协议服务",
    }, nil
}

func (c *CustomProtocolClient) ProtocolDataDomainOrganization(ctx context.Context, params string) (string, error) {
    // 实现数据域组织逻辑
    return "organized_data", nil
}

// 实现其他接口方法...
```

### 2. 注册自定义协议

```go
gateway := server.GetGateway()
customClient := &CustomProtocolClient{}
gateway.AddService("Custom Protocol", customClient)
```

## 管理和监控

### 获取服务状态

```go
gateway := server.GetGateway()
status := gateway.GetServiceStatus()
for serviceName, isHealthy := range status {
    fmt.Printf("%s: %v\n", serviceName, isHealthy)
}
```

### 列出所有服务

```go
services := gateway.ListServices()
fmt.Printf("注册的服务: %v\n", services)
```

### 动态管理服务

```go
// 添加服务
gateway.AddService("New Protocol", newClient)

// 移除服务
gateway.RemoveService("Old Protocol")
```

## API 接口

### BaseProtocolService

- `GetServiceInfo`: 获取服务信息
- `ProtocolDataDomainOrganization`: 协议数据域组织
- `ProtocolOrganization`: 协议组织
- `ProtocolParsing`: 协议解析
- `ProtocolDataDomainParsing`: 协议数据域解析

### 请求参数

所有协议操作请求都包含 `name` 参数，用于指定要使用的协议服务：

- `name`: 协议服务名称 (如 "DL/T 698.45", "Modbus" 等)
- `params`: 协议相关参数 (JSON 格式)
- `frame`: 协议帧数据 (字符串格式)
- `datadomain`: 协议数据域 (字符串格式)

## 错误处理

网关会处理以下错误情况：

1. **服务不存在**: 返回 "service not found" 错误
2. **服务不可用**: 自动尝试故障转移
3. **参数错误**: 返回参数格式错误信息
4. **连接超时**: 根据配置进行重试

## 日志记录

网关会记录以下信息：

- 服务注册和注销
- 健康检查结果
- 错误和异常情况
- 服务调用统计

## 性能优化

- 连接池管理
- 请求缓存
- 异步健康检查
- 服务预热

## 部署建议

1. **容器化部署**: 支持 Docker 容器部署
2. **服务发现**: 集成 Consul 或 etcd
3. **负载均衡**: 支持多实例部署
4. **监控告警**: 集成 Prometheus 监控

## 示例代码

详细的使用示例请参考 `protocol_gateway_example.go` 文件。
