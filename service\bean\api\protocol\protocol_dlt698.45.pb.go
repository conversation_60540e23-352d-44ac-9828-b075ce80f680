//*
// DL/T 698.45 通信协议服务
// DL/T 698.45 的通信协议组织及解析服务

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/protocol/protocol_dlt698.45.proto

package protocol

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务信息获取请求
type GetSvcInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSvcInfoReq) Reset() {
	*x = GetSvcInfoReq{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSvcInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSvcInfoReq) ProtoMessage() {}

func (x *GetSvcInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSvcInfoReq.ProtoReflect.Descriptor instead.
func (*GetSvcInfoReq) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{0}
}

// 服务信息获取响应
type GetSvcInfoResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务信息
	Services      *ProtServiceInfo `protobuf:"bytes,1,opt,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSvcInfoResp) Reset() {
	*x = GetSvcInfoResp{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSvcInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSvcInfoResp) ProtoMessage() {}

func (x *GetSvcInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSvcInfoResp.ProtoReflect.Descriptor instead.
func (*GetSvcInfoResp) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{1}
}

func (x *GetSvcInfoResp) GetServices() *ProtServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

// * 通讯协议数据域组织请求 Communication Protocol DataDomain Organization
// DL/T 698.45 协议为例：
//
//	{
//	    "type": 0x0100,      // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
//	    "addr": "",          // 地址
//	    "data": {
//	        ... // 根据请求类型不同，传递不同的参数
//	    },
//	    "timetag": 0        // 时间标签，0 无时间标签（默认无时间标签，可不传）
//	}
type CPDOReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织参数, JSON 格式
	Params        string `protobuf:"bytes,1,opt,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDOReq) Reset() {
	*x = CPDOReq{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDOReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDOReq) ProtoMessage() {}

func (x *CPDOReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDOReq.ProtoReflect.Descriptor instead.
func (*CPDOReq) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{2}
}

func (x *CPDOReq) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

// 协议数据域组织响应
type CPDOResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织后的数据, 字符串格式
	Datadomain    string `protobuf:"bytes,1,opt,name=datadomain,proto3" json:"datadomain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDOResp) Reset() {
	*x = CPDOResp{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDOResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDOResp) ProtoMessage() {}

func (x *CPDOResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDOResp.ProtoReflect.Descriptor instead.
func (*CPDOResp) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{3}
}

func (x *CPDOResp) GetDatadomain() string {
	if x != nil {
		return x.Datadomain
	}
	return ""
}

// * 通讯协议组织请求 Communication Protocol Organization
// DL/T 698.45 协议为例：
// 1. 先组织的数据域（APDU）
//
//	"params": {
//	    "datadomain": ""
//	}
//
// 2. 传入不同数据直接组织协议报文
//
//	"params": {
//	    "type": 0x0100,  // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
//	    "addr": "",      // 地址
//	    "data": {
//	        ... // 根据请求类型不同，传递不同的参数
//	    },
//	    "timetag": 0    // 时间标签，0 无时间标签（默认无时间标签，可不传）
//	}
type CPOReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织参数, JSON 格式
	Params        string `protobuf:"bytes,1,opt,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPOReq) Reset() {
	*x = CPOReq{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPOReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPOReq) ProtoMessage() {}

func (x *CPOReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPOReq.ProtoReflect.Descriptor instead.
func (*CPOReq) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{4}
}

func (x *CPOReq) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

// 协议组织响应
type CPOResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织后的数据, 字符串格式
	Frame         string `protobuf:"bytes,1,opt,name=frame,proto3" json:"frame,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPOResp) Reset() {
	*x = CPOResp{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPOResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPOResp) ProtoMessage() {}

func (x *CPOResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPOResp.ProtoReflect.Descriptor instead.
func (*CPOResp) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{5}
}

func (x *CPOResp) GetFrame() string {
	if x != nil {
		return x.Frame
	}
	return ""
}

// 通讯协议解析请求 Communication Protocol Parsing
type CPPReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 待解析的数据, 字符串格式
	Frame         string `protobuf:"bytes,1,opt,name=frame,proto3" json:"frame,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPPReq) Reset() {
	*x = CPPReq{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPPReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPPReq) ProtoMessage() {}

func (x *CPPReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPPReq.ProtoReflect.Descriptor instead.
func (*CPPReq) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{6}
}

func (x *CPPReq) GetFrame() string {
	if x != nil {
		return x.Frame
	}
	return ""
}

// 协议解析响应
type CPPResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 解析后的数据, JSON 格式
	Data          string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPPResp) Reset() {
	*x = CPPResp{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPPResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPPResp) ProtoMessage() {}

func (x *CPPResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPPResp.ProtoReflect.Descriptor instead.
func (*CPPResp) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{7}
}

func (x *CPPResp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// 通讯协议数据域解析请求
type CPDReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 待解析的数据, 字符串格式
	Datadomain    string `protobuf:"bytes,1,opt,name=datadomain,proto3" json:"datadomain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDReq) Reset() {
	*x = CPDReq{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDReq) ProtoMessage() {}

func (x *CPDReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDReq.ProtoReflect.Descriptor instead.
func (*CPDReq) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{8}
}

func (x *CPDReq) GetDatadomain() string {
	if x != nil {
		return x.Datadomain
	}
	return ""
}

// 协议数据域解析响应
type CPDResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 解析后的数据, JSON 格式
	Data          string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDResp) Reset() {
	*x = CPDResp{}
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDResp) ProtoMessage() {}

func (x *CPDResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_dlt698_45_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDResp.ProtoReflect.Descriptor instead.
func (*CPDResp) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP(), []int{9}
}

func (x *CPDResp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_api_protocol_protocol_dlt698_45_proto protoreflect.FileDescriptor

const file_api_protocol_protocol_dlt698_45_proto_rawDesc = "" +
	"\n" +
	"%api/protocol/protocol_dlt698.45.proto\x12\bprotocol\x1a\x1bapi/protocol/protocol.proto\"\x0f\n" +
	"\rGetSvcInfoReq\"G\n" +
	"\x0eGetSvcInfoResp\x125\n" +
	"\bservices\x18\x01 \x01(\v2\x19.protocol.ProtServiceInfoR\bservices\"!\n" +
	"\aCPDOReq\x12\x16\n" +
	"\x06params\x18\x01 \x01(\tR\x06params\"*\n" +
	"\bCPDOResp\x12\x1e\n" +
	"\n" +
	"datadomain\x18\x01 \x01(\tR\n" +
	"datadomain\" \n" +
	"\x06CPOReq\x12\x16\n" +
	"\x06params\x18\x01 \x01(\tR\x06params\"\x1f\n" +
	"\aCPOResp\x12\x14\n" +
	"\x05frame\x18\x01 \x01(\tR\x05frame\"\x1e\n" +
	"\x06CPPReq\x12\x14\n" +
	"\x05frame\x18\x01 \x01(\tR\x05frame\"\x1d\n" +
	"\aCPPResp\x12\x12\n" +
	"\x04data\x18\x01 \x01(\tR\x04data\"(\n" +
	"\x06CPDReq\x12\x1e\n" +
	"\n" +
	"datadomain\x18\x01 \x01(\tR\n" +
	"datadomain\"\x1d\n" +
	"\aCPDResp\x12\x12\n" +
	"\x04data\x18\x01 \x01(\tR\x04data2\x90\x02\n" +
	"\x0fDLT69845ProtSvc\x12?\n" +
	"\n" +
	"GetSvcInfo\x12\x17.protocol.GetSvcInfoReq\x1a\x18.protocol.GetSvcInfoResp\x120\n" +
	"\aProtDDO\x12\x11.protocol.CPDOReq\x1a\x12.protocol.CPDOResp\x12.\n" +
	"\aProtDDP\x12\x10.protocol.CPDReq\x1a\x11.protocol.CPDResp\x12,\n" +
	"\x05ProtO\x12\x10.protocol.CPOReq\x1a\x11.protocol.CPOResp\x12,\n" +
	"\x05ProtP\x12\x10.protocol.CPPReq\x1a\x11.protocol.CPPRespB&Z$tp.service/service/bean/api/protocolb\x06proto3"

var (
	file_api_protocol_protocol_dlt698_45_proto_rawDescOnce sync.Once
	file_api_protocol_protocol_dlt698_45_proto_rawDescData []byte
)

func file_api_protocol_protocol_dlt698_45_proto_rawDescGZIP() []byte {
	file_api_protocol_protocol_dlt698_45_proto_rawDescOnce.Do(func() {
		file_api_protocol_protocol_dlt698_45_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_protocol_protocol_dlt698_45_proto_rawDesc), len(file_api_protocol_protocol_dlt698_45_proto_rawDesc)))
	})
	return file_api_protocol_protocol_dlt698_45_proto_rawDescData
}

var file_api_protocol_protocol_dlt698_45_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_protocol_protocol_dlt698_45_proto_goTypes = []any{
	(*GetSvcInfoReq)(nil),   // 0: protocol.GetSvcInfoReq
	(*GetSvcInfoResp)(nil),  // 1: protocol.GetSvcInfoResp
	(*CPDOReq)(nil),         // 2: protocol.CPDOReq
	(*CPDOResp)(nil),        // 3: protocol.CPDOResp
	(*CPOReq)(nil),          // 4: protocol.CPOReq
	(*CPOResp)(nil),         // 5: protocol.CPOResp
	(*CPPReq)(nil),          // 6: protocol.CPPReq
	(*CPPResp)(nil),         // 7: protocol.CPPResp
	(*CPDReq)(nil),          // 8: protocol.CPDReq
	(*CPDResp)(nil),         // 9: protocol.CPDResp
	(*ProtServiceInfo)(nil), // 10: protocol.ProtServiceInfo
}
var file_api_protocol_protocol_dlt698_45_proto_depIdxs = []int32{
	10, // 0: protocol.GetSvcInfoResp.services:type_name -> protocol.ProtServiceInfo
	0,  // 1: protocol.DLT69845ProtSvc.GetSvcInfo:input_type -> protocol.GetSvcInfoReq
	2,  // 2: protocol.DLT69845ProtSvc.ProtDDO:input_type -> protocol.CPDOReq
	8,  // 3: protocol.DLT69845ProtSvc.ProtDDP:input_type -> protocol.CPDReq
	4,  // 4: protocol.DLT69845ProtSvc.ProtO:input_type -> protocol.CPOReq
	6,  // 5: protocol.DLT69845ProtSvc.ProtP:input_type -> protocol.CPPReq
	1,  // 6: protocol.DLT69845ProtSvc.GetSvcInfo:output_type -> protocol.GetSvcInfoResp
	3,  // 7: protocol.DLT69845ProtSvc.ProtDDO:output_type -> protocol.CPDOResp
	9,  // 8: protocol.DLT69845ProtSvc.ProtDDP:output_type -> protocol.CPDResp
	5,  // 9: protocol.DLT69845ProtSvc.ProtO:output_type -> protocol.CPOResp
	7,  // 10: protocol.DLT69845ProtSvc.ProtP:output_type -> protocol.CPPResp
	6,  // [6:11] is the sub-list for method output_type
	1,  // [1:6] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_api_protocol_protocol_dlt698_45_proto_init() }
func file_api_protocol_protocol_dlt698_45_proto_init() {
	if File_api_protocol_protocol_dlt698_45_proto != nil {
		return
	}
	file_api_protocol_protocol_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_protocol_protocol_dlt698_45_proto_rawDesc), len(file_api_protocol_protocol_dlt698_45_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_protocol_protocol_dlt698_45_proto_goTypes,
		DependencyIndexes: file_api_protocol_protocol_dlt698_45_proto_depIdxs,
		MessageInfos:      file_api_protocol_protocol_dlt698_45_proto_msgTypes,
	}.Build()
	File_api_protocol_protocol_dlt698_45_proto = out.File
	file_api_protocol_protocol_dlt698_45_proto_goTypes = nil
	file_api_protocol_protocol_dlt698_45_proto_depIdxs = nil
}
