//*
// 通信协议服务网关
// 根据请求中的协议类型重定向到指定服务

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/protocol/protocol.proto

package protocol

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BaseProtocolService_GetServiceInfo_FullMethodName                 = "/protocol.BaseProtocolService/GetServiceInfo"
	BaseProtocolService_ProtocolDataDomainOrganization_FullMethodName = "/protocol.BaseProtocolService/ProtocolDataDomainOrganization"
	BaseProtocolService_ProtocolOrganization_FullMethodName           = "/protocol.BaseProtocolService/ProtocolOrganization"
	BaseProtocolService_ProtocolParsing_FullMethodName                = "/protocol.BaseProtocolService/ProtocolParsing"
	BaseProtocolService_ProtocolDataDomainParsing_FullMethodName      = "/protocol.BaseProtocolService/ProtocolDataDomainParsing"
)

// BaseProtocolServiceClient is the client API for BaseProtocolService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// *
// 通信协议服务网关
// 对检测服务提供协议报文的组织及解析支持
type BaseProtocolServiceClient interface {
	// *
	// 获取服务信息
	// 参数
	//   - name: 服务名称，如果为空("")则获取所有服务信息
	//
	// 返回
	//   - services: 服务信息列表
	GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error)
	// *
	// 协议数据域组织
	// 参数
	//   - type: 协议类型
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - datadomain: 组织后的数据, 字符串格式
	ProtocolDataDomainOrganization(ctx context.Context, in *CPDORequest, opts ...grpc.CallOption) (*CPDOResponse, error)
	// *
	// 协议组织
	// 参数
	//   - type: 协议类型
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - frame: 组织后的数据, 字符串格式
	ProtocolOrganization(ctx context.Context, in *CPORequest, opts ...grpc.CallOption) (*CPOResponse, error)
	// *
	// 协议解析
	// 参数
	//   - type: 协议类型
	//   - frame: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtocolParsing(ctx context.Context, in *CPPRequest, opts ...grpc.CallOption) (*CPPResponse, error)
	// *
	// 协议数据域解析
	// 参数
	//   - type: 协议类型
	//   - datadomain: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtocolDataDomainParsing(ctx context.Context, in *CPDRequest, opts ...grpc.CallOption) (*CPDResponse, error)
}

type baseProtocolServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBaseProtocolServiceClient(cc grpc.ClientConnInterface) BaseProtocolServiceClient {
	return &baseProtocolServiceClient{cc}
}

func (c *baseProtocolServiceClient) GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceInfoResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_GetServiceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseProtocolServiceClient) ProtocolDataDomainOrganization(ctx context.Context, in *CPDORequest, opts ...grpc.CallOption) (*CPDOResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPDOResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_ProtocolDataDomainOrganization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseProtocolServiceClient) ProtocolOrganization(ctx context.Context, in *CPORequest, opts ...grpc.CallOption) (*CPOResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPOResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_ProtocolOrganization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseProtocolServiceClient) ProtocolParsing(ctx context.Context, in *CPPRequest, opts ...grpc.CallOption) (*CPPResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPPResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_ProtocolParsing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseProtocolServiceClient) ProtocolDataDomainParsing(ctx context.Context, in *CPDRequest, opts ...grpc.CallOption) (*CPDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPDResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_ProtocolDataDomainParsing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BaseProtocolServiceServer is the server API for BaseProtocolService service.
// All implementations must embed UnimplementedBaseProtocolServiceServer
// for forward compatibility.
//
// *
// 通信协议服务网关
// 对检测服务提供协议报文的组织及解析支持
type BaseProtocolServiceServer interface {
	// *
	// 获取服务信息
	// 参数
	//   - name: 服务名称，如果为空("")则获取所有服务信息
	//
	// 返回
	//   - services: 服务信息列表
	GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error)
	// *
	// 协议数据域组织
	// 参数
	//   - type: 协议类型
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - datadomain: 组织后的数据, 字符串格式
	ProtocolDataDomainOrganization(context.Context, *CPDORequest) (*CPDOResponse, error)
	// *
	// 协议组织
	// 参数
	//   - type: 协议类型
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - frame: 组织后的数据, 字符串格式
	ProtocolOrganization(context.Context, *CPORequest) (*CPOResponse, error)
	// *
	// 协议解析
	// 参数
	//   - type: 协议类型
	//   - frame: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtocolParsing(context.Context, *CPPRequest) (*CPPResponse, error)
	// *
	// 协议数据域解析
	// 参数
	//   - type: 协议类型
	//   - datadomain: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtocolDataDomainParsing(context.Context, *CPDRequest) (*CPDResponse, error)
	mustEmbedUnimplementedBaseProtocolServiceServer()
}

// UnimplementedBaseProtocolServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBaseProtocolServiceServer struct{}

func (UnimplementedBaseProtocolServiceServer) GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceInfo not implemented")
}
func (UnimplementedBaseProtocolServiceServer) ProtocolDataDomainOrganization(context.Context, *CPDORequest) (*CPDOResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolDataDomainOrganization not implemented")
}
func (UnimplementedBaseProtocolServiceServer) ProtocolOrganization(context.Context, *CPORequest) (*CPOResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolOrganization not implemented")
}
func (UnimplementedBaseProtocolServiceServer) ProtocolParsing(context.Context, *CPPRequest) (*CPPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolParsing not implemented")
}
func (UnimplementedBaseProtocolServiceServer) ProtocolDataDomainParsing(context.Context, *CPDRequest) (*CPDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolDataDomainParsing not implemented")
}
func (UnimplementedBaseProtocolServiceServer) mustEmbedUnimplementedBaseProtocolServiceServer() {}
func (UnimplementedBaseProtocolServiceServer) testEmbeddedByValue()                             {}

// UnsafeBaseProtocolServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BaseProtocolServiceServer will
// result in compilation errors.
type UnsafeBaseProtocolServiceServer interface {
	mustEmbedUnimplementedBaseProtocolServiceServer()
}

func RegisterBaseProtocolServiceServer(s grpc.ServiceRegistrar, srv BaseProtocolServiceServer) {
	// If the following call pancis, it indicates UnimplementedBaseProtocolServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BaseProtocolService_ServiceDesc, srv)
}

func _BaseProtocolService_GetServiceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).GetServiceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_GetServiceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).GetServiceInfo(ctx, req.(*GetServiceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseProtocolService_ProtocolDataDomainOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPDORequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).ProtocolDataDomainOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_ProtocolDataDomainOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).ProtocolDataDomainOrganization(ctx, req.(*CPDORequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseProtocolService_ProtocolOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPORequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).ProtocolOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_ProtocolOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).ProtocolOrganization(ctx, req.(*CPORequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseProtocolService_ProtocolParsing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).ProtocolParsing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_ProtocolParsing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).ProtocolParsing(ctx, req.(*CPPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseProtocolService_ProtocolDataDomainParsing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).ProtocolDataDomainParsing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_ProtocolDataDomainParsing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).ProtocolDataDomainParsing(ctx, req.(*CPDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BaseProtocolService_ServiceDesc is the grpc.ServiceDesc for BaseProtocolService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BaseProtocolService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "protocol.BaseProtocolService",
	HandlerType: (*BaseProtocolServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServiceInfo",
			Handler:    _BaseProtocolService_GetServiceInfo_Handler,
		},
		{
			MethodName: "ProtocolDataDomainOrganization",
			Handler:    _BaseProtocolService_ProtocolDataDomainOrganization_Handler,
		},
		{
			MethodName: "ProtocolOrganization",
			Handler:    _BaseProtocolService_ProtocolOrganization_Handler,
		},
		{
			MethodName: "ProtocolParsing",
			Handler:    _BaseProtocolService_ProtocolParsing_Handler,
		},
		{
			MethodName: "ProtocolDataDomainParsing",
			Handler:    _BaseProtocolService_ProtocolDataDomainParsing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/protocol/protocol.proto",
}
