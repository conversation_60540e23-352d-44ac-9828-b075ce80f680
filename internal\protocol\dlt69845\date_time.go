package dlt69845

import "tp.service/internal/protocol/axdr"

type DateTime *axdr.Sequence
type DateTimeElement struct {
	Year         LongUnsigned
	Month        Unsigned
	DayOfMonth   Unsigned
	DayOfWeek    Unsigned
	Hour         Unsigned
	Minute       Unsigned
	Second       Unsigned
	Milliseconds LongUnsigned
}

func NewDateTime(element DateTimeElement) DateTime {
	elements := []axdr.SequenceElement{
		{Value: &element.Year},
		{Value: &element.Month},
		{Value: &element.DayOfMonth},
		{Value: &element.DayOfWeek},
		{Value: &element.Hour},
		{Value: &element.Minute},
		{Value: &element.Second},
		{Value: &element.Milliseconds},
	}
	return DateTime(axdr.NewSequence(elements))
}

// type DateTime struct {
// 	Year         LongUnsigned
// 	Month        Unsigned
// 	DayOfMonth   Unsigned
// 	DayOfWeek    Unsigned
// 	Hour         Unsigned
// 	Minute       Unsigned
// 	Second       Unsigned
// 	Milliseconds LongUnsigned
// }

// func (v *DateTime) ToSequence() *axdr.Sequence {
// 	elements := []axdr.SequenceElement{
// 		{Value: &v.Year},
// 		{Value: &v.Month},
// 		{Value: &v.DayOfMonth},
// 		{Value: &v.DayOfWeek},
// 		{Value: &v.Hour},
// 		{Value: &v.Minute},
// 		{Value: &v.Second},
// 		{Value: &v.Milliseconds},
// 	}
// 	return axdr.NewSequence(elements)
// }
