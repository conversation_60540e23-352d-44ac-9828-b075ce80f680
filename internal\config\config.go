package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v2"
)

// Config 表示应用程序配置
type Config struct {
	Detector  DetectorConfig   `yaml:"detector"`
	Input     InputConfig      `yaml:"input"`
	Output    OutputConfig     `yaml:"output"`
	Positions []PositionConfig `yaml:"positions,omitempty"`
	// 用于解析单个 position 节点
	Position *PositionConfig `yaml:"position,omitempty"`

	Items []ItemConfig `yaml:"items"`
	// 用于解析单个 item 节点
	Item *ItemConfig `yaml:"item,omitempty"`

	// 日志配置
	Logging LoggingConfig `yaml:"logging"`
}

// DetectorConfig 表示检测器配置
type DetectorConfig struct {
	Sensitivity float64 `yaml:"sensitivity"`
	Threshold   float64 `yaml:"threshold"`
	Mode        string  `yaml:"mode"`
}

// InputConfig 表示输入配置
type InputConfig struct {
	Source string `yaml:"source"`
	Path   string `yaml:"path"`
}

// OutputConfig 表示输出配置
type OutputConfig struct {
	Format string `yaml:"format"`
	Path   string `yaml:"path"`
}

// PositionConfig 表示 Position 节点配置
type PositionConfig struct {
	ID          int            `yaml:"id"`
	Enable      bool           `yaml:"enable"`
	SerialPins  [5]interface{} `yaml:"serial_pins"`
	SerialRs485 [5]interface{} `yaml:"serial_rs485"`
}

// ItemConfig 表示 Item 节点配置
type ItemConfig struct {
	ID          int    `yaml:"id"`
	Enable      bool   `yaml:"enable"`
	Description string `yaml:"description"`
}

// LoggingConfig 表示日志配置
type LoggingConfig struct {
	Level      string `yaml:"level"`       // 日志级别：DEBUG, INFO, WARN, ERROR, FATAL
	FilePath   string `yaml:"file_path"`   // 日志文件路径
	MaxSize    int64  `yaml:"max_size"`    // 单个日志文件最大大小（字节）
	MaxBackups int    `yaml:"max_backups"` // 最大保留的日志文件数
	Console    bool   `yaml:"console"`     // 是否同时输出到控制台
}

// LoadConfig 从文件加载配置
func LoadConfig(path string) (*Config, error) {
	// 检查环境变量中是否指定了配置文件路径
	if envPath := os.Getenv("DETECTOR_CONFIG_PATH"); envPath != "" {
		// 如果环境变量指定了目录而不是文件，则拼接文件名
		if stat, err := os.Stat(envPath); err == nil && stat.IsDir() {
			envPath = filepath.Join(envPath, path)
		}

		// 尝试从环境变量指定的路径加载
		if _, err := os.Stat(envPath); err == nil {
			// 尝试使用特殊处理函数解析多个 position 节点
			config, err := ParseMultiple(envPath)
			if err == nil && len(config.GetPositions()) > 0 {
				return config, nil
			}

			// 如果特殊处理失败，回退到常规解析
			return loadConfigFromPath(envPath)
		}
	}

	// 如果提供了绝对路径或相对路径，直接尝试加载
	if _, err := os.Stat(path); err == nil {
		// 尝试使用特殊处理函数解析多个 position 节点
		config, err := ParseMultiple(path)
		if err == nil && len(config.GetPositions()) > 0 {
			return config, nil
		}

		// 如果特殊处理失败，回退到常规解析
		return loadConfigFromPath(path)
	}

	// 尝试从多个位置查找配置文件
	configPath, err := FindConfig(path)
	if err != nil {
		return nil, err
	}

	// 尝试使用特殊处理函数解析多个节点
	config, err := ParseMultiple(configPath)
	if err == nil && len(config.GetPositions()) > 0 {
		return config, nil
	}

	// 如果特殊处理失败，回退到常规解析
	return loadConfigFromPath(configPath)
}

// loadConfigFromPath 从指定路径加载配置文件
func loadConfigFromPath(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 首先尝试常规解析
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		// 如果常规解析失败，尝试特殊处理多个节点
		return parseConfigWithMultiple(data)
	}

	return &config, nil
}

// parseConfigWithMultiple 解析包含多个 position 节点的配置文件
func parseConfigWithMultiple(data []byte) (*Config, error) {
	// 首先解析基本配置（不包括 position 节点）
	var config Config

	// 创建一个临时结构体，只包含基本配置
	type BasicConfig struct {
		Detector DetectorConfig `yaml:"detector"`
		Input    InputConfig    `yaml:"input"`
		Output   OutputConfig   `yaml:"output"`
	}

	var basicConfig BasicConfig
	if err := yaml.Unmarshal(data, &basicConfig); err != nil {
		return nil, fmt.Errorf("解析基本配置失败: %w", err)
	}

	// 将基本配置复制到最终配置中
	config.Detector = basicConfig.Detector
	config.Input = basicConfig.Input
	config.Output = basicConfig.Output

	// 创建一个临时结构体，用于解析单个 position 节点
	type TempConfig struct {
		Position PositionConfig `yaml:"position"`
	}

	// 创建一个临时结构体，用于解析单个 item 节点
	type TempItemConfig struct {
		Item ItemConfig `yaml:"item"`
	}

	// 分割 YAML 文档，查找所有 position 节点
	yamlContent := string(data)
	lines := strings.Split(yamlContent, "\n")

	// 遍历每一行，查找 position: 开头的行
	for i, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "position:" {
			// 找到一个 position 节点，提取这个节点的内容
			positionYaml := extractPositionYaml(lines, i)

			// 解析这个 position 节点
			var tempConfig TempConfig
			if err := yaml.Unmarshal([]byte(positionYaml), &tempConfig); err == nil {
				// 添加到 positions 数组
				config.Positions = append(config.Positions, tempConfig.Position)
			}
		} else if trimmedLine == "item:" {
			// 找到一个 item 节点，提取这个节点的内容
			itemYaml := extractItemYaml(lines, i)

			// 解析这个 item 节点
			var tempItemConfig TempItemConfig
			if err := yaml.Unmarshal([]byte(itemYaml), &tempItemConfig); err == nil {
				// 添加到 items 数组
				config.Items = append(config.Items, tempItemConfig.Item)
			}
		}
	}

	// 如果没有找到任何 position 节点或 item 节点，尝试直接解析整个文件
	if len(config.Positions) == 0 || len(config.Items) == 0 {
		// 创建一个临时结构体，用于解析整个文件
		type FullConfig struct {
			Detector  DetectorConfig   `yaml:"detector"`
			Input     InputConfig      `yaml:"input"`
			Output    OutputConfig     `yaml:"output"`
			Positions []PositionConfig `yaml:"positions,omitempty"`
			Position  *PositionConfig  `yaml:"position,omitempty"`
			Items     []ItemConfig     `yaml:"items,omitempty"`
			Item      *ItemConfig      `yaml:"item,omitempty"`
		}

		var fullConfig FullConfig
		if err := yaml.Unmarshal(data, &fullConfig); err == nil {
			// 如果有 positions 数组，使用它
			if len(fullConfig.Positions) > 0 {
				config.Positions = fullConfig.Positions
			}
			// 如果有单个 position 节点，添加它
			if fullConfig.Position != nil {
				config.Positions = append(config.Positions, *fullConfig.Position)
			}

			// 如果有 items 数组，使用它
			if len(fullConfig.Items) > 0 {
				config.Items = fullConfig.Items
			}
			// 如果有单个 item 节点，添加它
			if fullConfig.Item != nil {
				config.Items = append(config.Items, *fullConfig.Item)
			}
		}
	}

	return &config, nil
}

// extractPositionYaml 从指定行开始提取一个完整的 position 节点
func extractPositionYaml(lines []string, startLine int) string {
	var sb strings.Builder

	// 添加 position: 行
	sb.WriteString(lines[startLine])
	sb.WriteString("\n")

	// 从下一行开始，添加所有缩进的行
	for i := startLine + 1; i < len(lines); i++ {
		line := lines[i]
		trimmed := strings.TrimSpace(line)

		// 如果遇到空行或者非缩进的行，说明 position 节点结束了
		if trimmed == "" || (len(trimmed) > 0 && !strings.HasPrefix(line, " ") && !strings.HasPrefix(line, "\t")) {
			break
		}

		// 添加这一行
		sb.WriteString(line)
		sb.WriteString("\n")
	}

	return sb.String()
}

// extractItemYaml 从指定行开始提取一个完整的 item 节点
func extractItemYaml(lines []string, startLine int) string {
	var sb strings.Builder

	// 添加 item: 行
	sb.WriteString(lines[startLine])
	sb.WriteString("\n")

	// 从下一行开始，添加所有缩进的行
	for i := startLine + 1; i < len(lines); i++ {
		line := lines[i]
		trimmed := strings.TrimSpace(line)

		// 如果遇到空行或者非缩进的行，说明 item 节点结束了
		if trimmed == "" || (len(trimmed) > 0 && !strings.HasPrefix(line, " ") && !strings.HasPrefix(line, "\t")) {
			break
		}

		// 添加这一行
		sb.WriteString(line)
		sb.WriteString("\n")
	}

	return sb.String()
}

// ParseMultiple 解析包含多个节点的配置文件
func ParseMultiple(configPath string) (*Config, error) {
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 使用特殊处理函数解析配置
	return parseConfigWithMultiple(data)
}

// FindConfig 在多个位置查找配置文件
func FindConfig(filename string) (string, error) {
	// 搜索路径列表，按优先级排序
	searchPaths := []string{
		".",                                 // 当前目录
		"..",                                // 上级目录
		"../config",                         // 上级目录的config子目录
		"config",                            // 当前目录的config子目录
		"../ExtendModuleDetection",          // 项目根目录
		filepath.Join("..", ".."),           // 上上级目录
		filepath.Join("..", "..", "config"), // 上上级目录的config子目录
		filepath.Join("..", "..", ".."),     // 上上上级目录
		filepath.Join("..", "..", "..", "config"), // 上上上级目录的config子目录
	}

	// 获取工作目录
	workDir, err := os.Getwd()
	if err == nil {
		// 添加工作目录的父目录到搜索路径
		parentDir := filepath.Dir(workDir)
		searchPaths = append(searchPaths, parentDir)
		searchPaths = append(searchPaths, filepath.Join(parentDir, "config"))
	}

	// 获取可执行文件所在目录
	execPath, err := os.Executable()
	if err == nil {
		execDir := filepath.Dir(execPath)
		// 添加可执行文件所在目录到搜索路径
		searchPaths = append(searchPaths, execDir)
		// 添加可执行文件所在目录的config子目录到搜索路径
		searchPaths = append(searchPaths, filepath.Join(execDir, "config"))
	}

	// 在每个搜索路径中查找配置文件
	for _, path := range searchPaths {
		configPath := filepath.Join(path, filename)
		if _, err := os.Stat(configPath); err == nil {
			return configPath, nil
		}
	}

	return "", fmt.Errorf("找不到配置文件: %s", filename)
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Detector: DetectorConfig{
			Sensitivity: 0.85,
			Threshold:   0.75,
			Mode:        "standard",
		},
		Input: InputConfig{
			Source: "camera",
			Path:   "/dev/video0",
		},
		Output: OutputConfig{
			Format: "json",
			Path:   "./results/",
		},
	}
}

// 获取 Position 节点信息
func (c *Config) GetPositions() []PositionConfig {
	// 如果已经有 Positions 数组，直接返回
	if len(c.Positions) > 0 {
		return c.Positions
	}

	// 如果有单个 Position 节点，将其添加到数组中
	var positions []PositionConfig
	if c.Position != nil {
		positions = append(positions, *c.Position)
	}

	return positions
}

// 获取 Item 节点信息
func (c *Config) GetItems() []ItemConfig {
	// 如果已经有 Items 数组，直接返回
	if len(c.Items) > 0 {
		return c.Items
	}

	// 如果有单个 Item 节点，将其添加到数组中
	var items []ItemConfig
	if c.Item != nil {
		items = append(items, *c.Item)
	}

	return items
}

// LoadConfigFromProjectRoot 从项目根目录加载配置文件
func LoadConfigFromProjectRoot(filename string) (*Config, error) {
	// 尝试从多个可能的项目根目录位置加载配置
	rootPaths := []string{
		".",                                   // 当前目录
		"..",                                  // 上级目录
		filepath.Join("..", ".."),             // 上上级目录
		filepath.Join("..", "..", ".."),       // 上上上级目录
		filepath.Join("..", "..", "..", ".."), // 上上上上级目录
	}

	// 获取工作目录
	workDir, err := os.Getwd()
	if err == nil {
		// 尝试查找项目根目录的标志（如 go.mod 文件）
		for _, rootPath := range rootPaths {
			testPath := filepath.Join(workDir, rootPath)
			// 检查是否存在 go.mod 文件（项目根目录标志）
			if _, err := os.Stat(filepath.Join(testPath, "go.mod")); err == nil {
				// 找到项目根目录，尝试加载配置
				configPath := filepath.Join(testPath, filename)
				if _, err := os.Stat(configPath); err == nil {
					// 尝试使用特殊处理函数解析多个节点
					config, err := ParseMultiple(configPath)
					if err == nil && len(config.GetItems()) > 0 {
						return config, nil
					}

					// 如果特殊处理失败，回退到常规解析
					return loadConfigFromPath(configPath)
				}
			}
		}
	}

	// 如果无法从项目根目录加载，回退到常规的查找方法
	return LoadConfig(filename)
}
