package axdr

import (
	"fmt"
	"strings"
)

// Type 返回 SEQUENCE 的 A-XDR 类型
func (s *Sequence) Type() AXDRType {
	return TypeSequence
}

// String 返回 SEQUENCE 的字符串表示
func (s *Sequence) String() string {
	var elements []string
	for i, elem := range s.Elements {
		elemStr := ""
		if elem.Value != nil {
			elemStr = elem.Value.String()
		} else {
			elemStr = "<nil>"
		}

		if elem.Optional != nil {
			if elem.Optional.Present {
				elemStr = fmt.Sprintf("OPTIONAL[present]: %s", elemStr)
			} else {
				elemStr = "OPTIONAL[absent]"
			}
		}

		if elem.Default != nil {
			if elem.Default.Present {
				elemStr = fmt.Sprintf("DEFAULT[present]: %s", elemStr)
			} else {
				elemStr = fmt.Sprintf("DEFAULT[default]: %s", elem.Default.DefaultValue.String())
			}
		}

		elements = append(elements, fmt.Sprintf("[%d]: %s", i, elemStr))
	}

	return fmt.Sprintf("SEQUENCE { %s }", strings.Join(elements, ", "))
}

// Encode 编码 SEQUENCE 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.9 节实现
func (s *Sequence) Encode() ([]byte, error) {
	var result []byte

	for i, elem := range s.Elements {
		// 处理可选项
		if elem.Optional != nil {
			// 编码存在标记（简单的单字节标记）
			if elem.Optional.Present {
				result = append(result, 0x01) // 存在标记
			} else {
				result = append(result, 0x00) // 不存在标记
			}

			// 如果存在，编码值
			if elem.Optional.Present && elem.Optional.Value != nil {
				valueBytes, err := elem.Optional.Value.Encode()
				if err != nil {
					return nil, fmt.Errorf("failed to encode optional value for element %d: %w", i, err)
				}
				result = append(result, valueBytes...)
			}
			continue
		}

		// 处理默认项
		if elem.Default != nil {
			// 编码存在标记（简单的单字节标记）
			if elem.Default.Present {
				result = append(result, 0x01) // 存在标记
				// 如果值不等于默认值，编码实际值
				if elem.Default.Value != nil {
					valueBytes, err := elem.Default.Value.Encode()
					if err != nil {
						return nil, fmt.Errorf("failed to encode default value for element %d: %w", i, err)
					}
					result = append(result, valueBytes...)
				}
			} else {
				result = append(result, 0x00) // 不存在标记
			}
			continue
		}

		// 处理普通元素
		if elem.Value == nil {
			return nil, fmt.Errorf("%w: sequence element %d is nil", ErrInvalidData, i)
		}

		valueBytes, err := elem.Value.Encode()
		if err != nil {
			return nil, fmt.Errorf("failed to encode sequence element %d: %w", i, err)
		}
		result = append(result, valueBytes...)
	}

	return result, nil
}

// Decode 从 A-XDR 格式解码 SEQUENCE
func (s *Sequence) Decode(data []byte) (int, error) {
	offset := 0

	for i := range s.Elements {
		elem := &s.Elements[i]

		// 处理可选项
		if elem.Optional != nil {
			// 解码存在标记（简单的单字节标记）
			if offset >= len(data) {
				return 0, ErrBufferTooSmall
			}

			elem.Optional.Present = data[offset] != 0x00
			offset++

			// 如果存在，解码值
			if elem.Optional.Present {
				if elem.Optional.Value == nil {
					return 0, fmt.Errorf("%w: optional value type not specified for element %d", ErrInvalidType, i)
				}

				consumed, err := elem.Optional.Value.Decode(data[offset:])
				if err != nil {
					return 0, fmt.Errorf("failed to decode optional value for element %d: %w", i, err)
				}
				offset += consumed
			}
			continue
		}

		// 处理默认项
		if elem.Default != nil {
			// 解码存在标记
			if offset >= len(data) {
				return 0, ErrBufferTooSmall
			}

			presenceFlag := NewBoolean(false)
			consumed, err := presenceFlag.Decode(data[offset:])
			if err != nil {
				return 0, fmt.Errorf("failed to decode default presence flag for element %d: %w", i, err)
			}
			offset += consumed

			elem.Default.Present = presenceFlag.Value

			// 如果存在，解码值
			if elem.Default.Present {
				if elem.Default.Value == nil {
					return 0, fmt.Errorf("%w: default value type not specified for element %d", ErrInvalidType, i)
				}

				consumed, err := elem.Default.Value.Decode(data[offset:])
				if err != nil {
					return 0, fmt.Errorf("failed to decode default value for element %d: %w", i, err)
				}
				offset += consumed
			}
			continue
		}

		// 处理普通元素
		if elem.Value == nil {
			return 0, fmt.Errorf("%w: sequence element %d type not specified", ErrInvalidType, i)
		}

		consumed, err := elem.Value.Decode(data[offset:])
		if err != nil {
			return 0, fmt.Errorf("failed to decode sequence element %d: %w", i, err)
		}
		offset += consumed
	}

	return offset, nil
}

// AddElement 添加普通元素到序列
func (s *Sequence) AddElement(value AXDRValue) {
	s.Elements = append(s.Elements, SequenceElement{Value: value})
}

// AddOptionalElement 添加可选元素到序列
func (s *Sequence) AddOptionalElement(present bool, value AXDRValue) {
	optional := NewOptional(present, value)
	s.Elements = append(s.Elements, SequenceElement{Optional: optional})
}

// AddDefaultElement 添加默认元素到序列
func (s *Sequence) AddDefaultElement(present bool, value, defaultValue AXDRValue) {
	defaultElem := NewDefault(present, value, defaultValue)
	s.Elements = append(s.Elements, SequenceElement{Default: defaultElem})
}

// GetElement 获取指定索引的元素值
func (s *Sequence) GetElement(index int) (AXDRValue, error) {
	if index < 0 || index >= len(s.Elements) {
		return nil, fmt.Errorf("%w: index %d out of range", ErrOutOfRange, index)
	}

	elem := s.Elements[index]

	if elem.Optional != nil {
		if elem.Optional.Present {
			return elem.Optional.Value, nil
		}
		return nil, fmt.Errorf("optional element %d is not present", index)
	}

	if elem.Default != nil {
		if elem.Default.Present {
			return elem.Default.Value, nil
		}
		return elem.Default.DefaultValue, nil
	}

	return elem.Value, nil
}

// SetElement 设置指定索引的元素值
func (s *Sequence) SetElement(index int, value AXDRValue) error {
	if index < 0 || index >= len(s.Elements) {
		return fmt.Errorf("%w: index %d out of range", ErrOutOfRange, index)
	}

	elem := &s.Elements[index]

	if elem.Optional != nil {
		elem.Optional.Present = true
		elem.Optional.Value = value
		return nil
	}

	if elem.Default != nil {
		elem.Default.Present = true
		elem.Default.Value = value
		return nil
	}

	elem.Value = value
	return nil
}
