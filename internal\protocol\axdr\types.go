// Package axdr implements A-XDR (A-eXternal Data Representation) encoding/decoding
// according to DL/T 790.6-2010 standard
package axdr

import (
	"errors"
	"time"
)

// A-XDR 基础类型定义
// 根据 DL/T 790.6-2010 标准实现

// AXDRType 表示 A-XDR 数据类型
type AXDRType byte

const (
	// 基础类型标识
	TypeInteger         AXDRType = 0x01 // INTEGER
	TypeBoolean         AXDRType = 0x02 // BOOLEAN
	TypeEnumerated      AXDRType = 0x03 // ENUMERATED
	TypeBitString       AXDRType = 0x04 // BIT STRING
	TypeByteString      AXDRType = 0x05 // BYTE STRING
	TypeChoice          AXDRType = 0x06 // CHOICE
	TypeSequence        AXDRType = 0x07 // SEQUENCE
	TypeSequenceOf      AXDRType = 0x08 // SEQUENCE OF
	TypeVisibleString   AXDRType = 0x09 // VisibleString
	TypeGeneralizedTime AXDRType = 0x0A // GeneralizedTime
	TypeNull            AXDRType = 0x0B // NULL
)

// 错误定义
var (
	ErrInvalidType    = errors.New("invalid A-XDR type")
	ErrInvalidLength  = errors.New("invalid length")
	ErrInvalidData    = errors.New("invalid data")
	ErrBufferTooSmall = errors.New("buffer too small")
	ErrOutOfRange     = errors.New("value out of range")
)

// AXDRValue 表示 A-XDR 值的接口
type AXDRValue interface {
	// Encode 编码为 A-XDR 格式
	Encode() ([]byte, error)

	// Decode 从 A-XDR 格式解码
	Decode(data []byte) (int, error) // 返回消耗的字节数

	// Type 返回 A-XDR 类型
	Type() AXDRType

	// String 返回字符串表示
	String() string
}

// Integer 表示 A-XDR INTEGER 类型
// 支持固定长度和可变长度编码
type Integer struct {
	Value         int64
	IsConstrained bool  // 是否受约束（固定长度）
	MinValue      int64 // 最小值（用于约束）
	MaxValue      int64 // 最大值（用于约束）
	ByteLength    int   // 字节长度（用于固定长度编码）
}

// Boolean 表示 A-XDR BOOLEAN 类型
type Boolean struct {
	Value bool
}

// Enumerated 表示 A-XDR ENUMERATED 类型
// 取值范围 0-255
type Enumerated struct {
	Value byte
}

// BitString 表示 A-XDR BIT STRING 类型
type BitString struct {
	Value         []byte
	BitLength     int  // 位长度
	IsConstrained bool // 是否规定大小
	Size          int  // 规定的大小（位数）
}

// ByteString 表示 A-XDR BYTE STRING 类型
type ByteString struct {
	Value         []byte
	IsConstrained bool // 是否规定大小
	Size          int  // 规定的大小（字节数）
}

// Choice 表示 A-XDR CHOICE 类型
type Choice struct {
	Tag   byte      // 选择标记
	Value AXDRValue // 选择的值
}

// Optional 表示可选项
type Optional struct {
	Present bool      // 是否存在
	Value   AXDRValue // 值（当存在时）
}

// Default 表示默认项
type Default struct {
	Present      bool      // 是否存在（不同于默认值）
	Value        AXDRValue // 值
	DefaultValue AXDRValue // 默认值
}

// Sequence 表示 A-XDR SEQUENCE 类型
type Sequence struct {
	Elements []SequenceElement
}

// SequenceElement 表示序列中的元素
type SequenceElement struct {
	Value    AXDRValue
	Optional *Optional // 可选项（如果是可选的）
	Default  *Default  // 默认项（如果有默认值）
}

// SequenceOf 表示 A-XDR SEQUENCE OF 类型
type SequenceOf struct {
	Elements      []AXDRValue
	IsConstrained bool // 是否规定大小
	Size          int  // 规定的大小
}

// VisibleString 表示 A-XDR VisibleString 类型
type VisibleString struct {
	Value string
}

// GeneralizedTime 表示 A-XDR GeneralizedTime 类型
type GeneralizedTime struct {
	Value time.Time
}

// Null 表示 A-XDR NULL 类型
type Null struct{}

// 工厂函数

// NewInteger 创建新的 Integer
func NewInteger(value int64) *Integer {
	return &Integer{
		Value:         value,
		IsConstrained: false,
	}
}

// NewConstrainedInteger 创建受约束的 Integer
func NewConstrainedInteger(value, min, max int64) *Integer {
	// 计算所需字节长度
	var byteLength int
	if min >= 0 {
		// 无符号整数
		maxVal := max
		if maxVal <= 255 {
			byteLength = 1
		} else if maxVal <= 65535 {
			byteLength = 2
		} else if maxVal <= 16777215 {
			byteLength = 3
		} else if maxVal <= 4294967295 {
			byteLength = 4
		} else {
			byteLength = 8
		}
	} else {
		// 有符号整数
		rangeVal := max - min
		if rangeVal <= 255 {
			byteLength = 1
		} else if rangeVal <= 65535 {
			byteLength = 2
		} else if rangeVal <= 16777215 {
			byteLength = 3
		} else if rangeVal <= 4294967295 {
			byteLength = 4
		} else {
			byteLength = 8
		}
	}

	return &Integer{
		Value:         value,
		IsConstrained: true,
		MinValue:      min,
		MaxValue:      max,
		ByteLength:    byteLength,
	}
}

// NewBoolean 创建新的 Boolean
func NewBoolean(value bool) *Boolean {
	return &Boolean{Value: value}
}

// NewEnumerated 创建新的 Enumerated
func NewEnumerated(value byte) *Enumerated {
	return &Enumerated{Value: value}
}

// NewBitString 创建新的 BitString
func NewBitString(value []byte, bitLength int) *BitString {
	return &BitString{
		Value:         value,
		BitLength:     bitLength,
		IsConstrained: false,
	}
}

// NewConstrainedBitString 创建规定大小的 BitString
func NewConstrainedBitString(value []byte, bitLength, size int) *BitString {
	return &BitString{
		Value:         value,
		BitLength:     bitLength,
		IsConstrained: true,
		Size:          size,
	}
}

// NewByteString 创建新的 ByteString
func NewByteString(value []byte) *ByteString {
	return &ByteString{
		Value:         value,
		IsConstrained: false,
	}
}

// NewConstrainedByteString 创建规定大小的 ByteString
func NewConstrainedByteString(value []byte, size int) *ByteString {
	return &ByteString{
		Value:         value,
		IsConstrained: true,
		Size:          size,
	}
}

// NewChoice 创建新的 Choice
func NewChoice(tag byte, value AXDRValue) *Choice {
	return &Choice{
		Tag:   tag,
		Value: value,
	}
}

// NewSequence 创建新的 Sequence
func NewSequence(elements []SequenceElement) *Sequence {
	return &Sequence{Elements: elements}
}

// NewSequenceOf 创建新的 SequenceOf
func NewSequenceOf(elements []AXDRValue) *SequenceOf {
	return &SequenceOf{
		Elements:      elements,
		IsConstrained: false,
	}
}

// NewConstrainedSequenceOf 创建规定大小的 SequenceOf
func NewConstrainedSequenceOf(elements []AXDRValue, size int) *SequenceOf {
	return &SequenceOf{
		Elements:      elements,
		IsConstrained: true,
		Size:          size,
	}
}

// NewVisibleString 创建新的 VisibleString
func NewVisibleString(value string) *VisibleString {
	return &VisibleString{Value: value}
}

// NewGeneralizedTime 创建新的 GeneralizedTime
func NewGeneralizedTime(value time.Time) *GeneralizedTime {
	return &GeneralizedTime{Value: value}
}

// NewNull 创建新的 Null
func NewNull() *Null {
	return &Null{}
}

// NewOptional 创建可选项
func NewOptional(present bool, value AXDRValue) *Optional {
	return &Optional{
		Present: present,
		Value:   value,
	}
}

// NewDefault 创建默认项
func NewDefault(present bool, value, defaultValue AXDRValue) *Default {
	return &Default{
		Present:      present,
		Value:        value,
		DefaultValue: defaultValue,
	}
}

// 辅助函数

// CalculateByteLength 计算整数所需的字节长度
// 考虑 A-XDR 编码中避免与长度域冲突的问题
func CalculateByteLength(value int64) int {
	if value == 0 {
		return 1
	}

	if value > 0 {
		// 正数：需要考虑最高位不能是1（避免被误认为负数）
		if value <= 127 {
			return 1
		} else if value <= 32767 {
			return 2
		} else if value <= 8388607 {
			return 3
		} else if value <= 2147483647 {
			return 4
		} else {
			return 8
		}
	} else {
		// 负数：使用2的补码表示，但要避免与长度域冲突
		// -128 的1字节表示是0x80，会与长度域冲突，所以需要2字节
		if value >= -127 {
			return 1
		} else if value >= -32768 {
			return 2
		} else if value >= -8388608 {
			return 3
		} else if value >= -2147483648 {
			return 4
		} else {
			return 8
		}
	}
}
