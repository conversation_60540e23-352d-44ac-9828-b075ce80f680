//*
// 检测服务网关
// 根据请求中的检测服务名称重定向到指定服务

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/detection/detection.proto

package detection

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BaseDetectionService_GetServiceInfo_FullMethodName      = "/detection.BaseDetectionService/GetServiceInfo"
	BaseDetectionService_DetectionInitialize_FullMethodName = "/detection.BaseDetectionService/DetectionInitialize"
	BaseDetectionService_DetectionCleanup_FullMethodName    = "/detection.BaseDetectionService/DetectionCleanup"
	BaseDetectionService_DetectionGetItems_FullMethodName   = "/detection.BaseDetectionService/DetectionGetItems"
	BaseDetectionService_DetectionStart_FullMethodName      = "/detection.BaseDetectionService/DetectionStart"
	BaseDetectionService_DetectionStop_FullMethodName       = "/detection.BaseDetectionService/DetectionStop"
	BaseDetectionService_DetectionRestart_FullMethodName    = "/detection.BaseDetectionService/DetectionRestart"
)

// BaseDetectionServiceClient is the client API for BaseDetectionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 检测服务网关
// 对客户端/浏览器提供统一的检测服务访问入口
type BaseDetectionServiceClient interface {
	// 获取检测服务信息
	// 根据 name 参数获取对应检测服务信息
	// 参数：
	//   - name: 检测服务名称，如果为空("")则获取所有服务信息
	//
	// 返回：
	//   - services: 检测服务信息列表
	GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error)
	// 检测服务初始化
	// 参数：
	//   - name: 检测服务名称
	//   - config: 检测配置, JSON 格式（待定, 不同检测服务约定不同）
	//
	// 返回：
	//   - token: 检测令牌，用于后续访问检测服务
	DetectionInitialize(ctx context.Context, in *DetectionInitRequest, opts ...grpc.CallOption) (*DetectionInitResponse, error)
	// 检测服务清理
	// 存在运行中的检测任务时，根据是否强制清理标志决定是否清理
	// 参数：
	//   - token: 检测令牌
	//   - force: 是否强制清理
	//
	// 返回：
	//   - 无
	DetectionCleanup(ctx context.Context, in *DetectionCleanupRequest, opts ...grpc.CallOption) (*DetectionCleanupResponse, error)
	// 获取检测项
	// 参数：
	//   - token: 检测令牌
	//
	// 返回：
	//   - items: 检测项列表
	DetectionGetItems(ctx context.Context, in *DetectionGetItemsRequest, opts ...grpc.CallOption) (*DetectionGetItemsResponse, error)
	// 启动检测
	// 参数：
	//   - token: 检测令牌
	//   - item_ids: 检测项 ID 集, 如果为空则启动所有检测项
	//
	// 返回：
	//   - 服务端流式响应（检测过程、结果信息通过流式响应返回）
	DetectionStart(ctx context.Context, in *DetectionStartRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DetectionStartStreamResponse], error)
	// 停止检测
	// 调用此接口停止检测时，检测服务并不会释放资源，可通过 restart 恢复检测工作
	// 若想终止检测不再继续执行，需调用 cleanup 接口释放资源
	// 参数：
	//   - token: 检测令牌
	//
	// 返回：
	//   - 无
	DetectionStop(ctx context.Context, in *DetectionStopRequest, opts ...grpc.CallOption) (*DetectionStopResponse, error)
	// 重新开始检测
	// 调用此接口会恢复调用 stop 接口暂停的检测工作，检测过程/结果信息仍有 start 接口的流式响应返回
	// 如果没有 stop 状态的检测任务，接口异常
	// 参数：
	//   - token: 检测令牌
	//
	// 返回：
	//   - 无
	DetectionRestart(ctx context.Context, in *DetectionRestartRequest, opts ...grpc.CallOption) (*DetectionRestartResponse, error)
}

type baseDetectionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBaseDetectionServiceClient(cc grpc.ClientConnInterface) BaseDetectionServiceClient {
	return &baseDetectionServiceClient{cc}
}

func (c *baseDetectionServiceClient) GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceInfoResponse)
	err := c.cc.Invoke(ctx, BaseDetectionService_GetServiceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDetectionServiceClient) DetectionInitialize(ctx context.Context, in *DetectionInitRequest, opts ...grpc.CallOption) (*DetectionInitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetectionInitResponse)
	err := c.cc.Invoke(ctx, BaseDetectionService_DetectionInitialize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDetectionServiceClient) DetectionCleanup(ctx context.Context, in *DetectionCleanupRequest, opts ...grpc.CallOption) (*DetectionCleanupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetectionCleanupResponse)
	err := c.cc.Invoke(ctx, BaseDetectionService_DetectionCleanup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDetectionServiceClient) DetectionGetItems(ctx context.Context, in *DetectionGetItemsRequest, opts ...grpc.CallOption) (*DetectionGetItemsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetectionGetItemsResponse)
	err := c.cc.Invoke(ctx, BaseDetectionService_DetectionGetItems_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDetectionServiceClient) DetectionStart(ctx context.Context, in *DetectionStartRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DetectionStartStreamResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &BaseDetectionService_ServiceDesc.Streams[0], BaseDetectionService_DetectionStart_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[DetectionStartRequest, DetectionStartStreamResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type BaseDetectionService_DetectionStartClient = grpc.ServerStreamingClient[DetectionStartStreamResponse]

func (c *baseDetectionServiceClient) DetectionStop(ctx context.Context, in *DetectionStopRequest, opts ...grpc.CallOption) (*DetectionStopResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetectionStopResponse)
	err := c.cc.Invoke(ctx, BaseDetectionService_DetectionStop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDetectionServiceClient) DetectionRestart(ctx context.Context, in *DetectionRestartRequest, opts ...grpc.CallOption) (*DetectionRestartResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetectionRestartResponse)
	err := c.cc.Invoke(ctx, BaseDetectionService_DetectionRestart_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BaseDetectionServiceServer is the server API for BaseDetectionService service.
// All implementations must embed UnimplementedBaseDetectionServiceServer
// for forward compatibility.
//
// 检测服务网关
// 对客户端/浏览器提供统一的检测服务访问入口
type BaseDetectionServiceServer interface {
	// 获取检测服务信息
	// 根据 name 参数获取对应检测服务信息
	// 参数：
	//   - name: 检测服务名称，如果为空("")则获取所有服务信息
	//
	// 返回：
	//   - services: 检测服务信息列表
	GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error)
	// 检测服务初始化
	// 参数：
	//   - name: 检测服务名称
	//   - config: 检测配置, JSON 格式（待定, 不同检测服务约定不同）
	//
	// 返回：
	//   - token: 检测令牌，用于后续访问检测服务
	DetectionInitialize(context.Context, *DetectionInitRequest) (*DetectionInitResponse, error)
	// 检测服务清理
	// 存在运行中的检测任务时，根据是否强制清理标志决定是否清理
	// 参数：
	//   - token: 检测令牌
	//   - force: 是否强制清理
	//
	// 返回：
	//   - 无
	DetectionCleanup(context.Context, *DetectionCleanupRequest) (*DetectionCleanupResponse, error)
	// 获取检测项
	// 参数：
	//   - token: 检测令牌
	//
	// 返回：
	//   - items: 检测项列表
	DetectionGetItems(context.Context, *DetectionGetItemsRequest) (*DetectionGetItemsResponse, error)
	// 启动检测
	// 参数：
	//   - token: 检测令牌
	//   - item_ids: 检测项 ID 集, 如果为空则启动所有检测项
	//
	// 返回：
	//   - 服务端流式响应（检测过程、结果信息通过流式响应返回）
	DetectionStart(*DetectionStartRequest, grpc.ServerStreamingServer[DetectionStartStreamResponse]) error
	// 停止检测
	// 调用此接口停止检测时，检测服务并不会释放资源，可通过 restart 恢复检测工作
	// 若想终止检测不再继续执行，需调用 cleanup 接口释放资源
	// 参数：
	//   - token: 检测令牌
	//
	// 返回：
	//   - 无
	DetectionStop(context.Context, *DetectionStopRequest) (*DetectionStopResponse, error)
	// 重新开始检测
	// 调用此接口会恢复调用 stop 接口暂停的检测工作，检测过程/结果信息仍有 start 接口的流式响应返回
	// 如果没有 stop 状态的检测任务，接口异常
	// 参数：
	//   - token: 检测令牌
	//
	// 返回：
	//   - 无
	DetectionRestart(context.Context, *DetectionRestartRequest) (*DetectionRestartResponse, error)
	mustEmbedUnimplementedBaseDetectionServiceServer()
}

// UnimplementedBaseDetectionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBaseDetectionServiceServer struct{}

func (UnimplementedBaseDetectionServiceServer) GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceInfo not implemented")
}
func (UnimplementedBaseDetectionServiceServer) DetectionInitialize(context.Context, *DetectionInitRequest) (*DetectionInitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectionInitialize not implemented")
}
func (UnimplementedBaseDetectionServiceServer) DetectionCleanup(context.Context, *DetectionCleanupRequest) (*DetectionCleanupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectionCleanup not implemented")
}
func (UnimplementedBaseDetectionServiceServer) DetectionGetItems(context.Context, *DetectionGetItemsRequest) (*DetectionGetItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectionGetItems not implemented")
}
func (UnimplementedBaseDetectionServiceServer) DetectionStart(*DetectionStartRequest, grpc.ServerStreamingServer[DetectionStartStreamResponse]) error {
	return status.Errorf(codes.Unimplemented, "method DetectionStart not implemented")
}
func (UnimplementedBaseDetectionServiceServer) DetectionStop(context.Context, *DetectionStopRequest) (*DetectionStopResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectionStop not implemented")
}
func (UnimplementedBaseDetectionServiceServer) DetectionRestart(context.Context, *DetectionRestartRequest) (*DetectionRestartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectionRestart not implemented")
}
func (UnimplementedBaseDetectionServiceServer) mustEmbedUnimplementedBaseDetectionServiceServer() {}
func (UnimplementedBaseDetectionServiceServer) testEmbeddedByValue()                              {}

// UnsafeBaseDetectionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BaseDetectionServiceServer will
// result in compilation errors.
type UnsafeBaseDetectionServiceServer interface {
	mustEmbedUnimplementedBaseDetectionServiceServer()
}

func RegisterBaseDetectionServiceServer(s grpc.ServiceRegistrar, srv BaseDetectionServiceServer) {
	// If the following call pancis, it indicates UnimplementedBaseDetectionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BaseDetectionService_ServiceDesc, srv)
}

func _BaseDetectionService_GetServiceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDetectionServiceServer).GetServiceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDetectionService_GetServiceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDetectionServiceServer).GetServiceInfo(ctx, req.(*GetServiceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDetectionService_DetectionInitialize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectionInitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDetectionServiceServer).DetectionInitialize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDetectionService_DetectionInitialize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDetectionServiceServer).DetectionInitialize(ctx, req.(*DetectionInitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDetectionService_DetectionCleanup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectionCleanupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDetectionServiceServer).DetectionCleanup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDetectionService_DetectionCleanup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDetectionServiceServer).DetectionCleanup(ctx, req.(*DetectionCleanupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDetectionService_DetectionGetItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectionGetItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDetectionServiceServer).DetectionGetItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDetectionService_DetectionGetItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDetectionServiceServer).DetectionGetItems(ctx, req.(*DetectionGetItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDetectionService_DetectionStart_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DetectionStartRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(BaseDetectionServiceServer).DetectionStart(m, &grpc.GenericServerStream[DetectionStartRequest, DetectionStartStreamResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type BaseDetectionService_DetectionStartServer = grpc.ServerStreamingServer[DetectionStartStreamResponse]

func _BaseDetectionService_DetectionStop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectionStopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDetectionServiceServer).DetectionStop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDetectionService_DetectionStop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDetectionServiceServer).DetectionStop(ctx, req.(*DetectionStopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDetectionService_DetectionRestart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectionRestartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDetectionServiceServer).DetectionRestart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDetectionService_DetectionRestart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDetectionServiceServer).DetectionRestart(ctx, req.(*DetectionRestartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BaseDetectionService_ServiceDesc is the grpc.ServiceDesc for BaseDetectionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BaseDetectionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "detection.BaseDetectionService",
	HandlerType: (*BaseDetectionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServiceInfo",
			Handler:    _BaseDetectionService_GetServiceInfo_Handler,
		},
		{
			MethodName: "DetectionInitialize",
			Handler:    _BaseDetectionService_DetectionInitialize_Handler,
		},
		{
			MethodName: "DetectionCleanup",
			Handler:    _BaseDetectionService_DetectionCleanup_Handler,
		},
		{
			MethodName: "DetectionGetItems",
			Handler:    _BaseDetectionService_DetectionGetItems_Handler,
		},
		{
			MethodName: "DetectionStop",
			Handler:    _BaseDetectionService_DetectionStop_Handler,
		},
		{
			MethodName: "DetectionRestart",
			Handler:    _BaseDetectionService_DetectionRestart_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "DetectionStart",
			Handler:       _BaseDetectionService_DetectionStart_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "api/detection/detection.proto",
}
