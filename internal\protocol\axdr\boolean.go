package axdr

import "fmt"

// Type 返回 BOOLEAN 的 A-XDR 类型
func (b *Boolean) Type() AXDRType {
	return TypeBoolean
}

// String 返回 BOOLEAN 的字符串表示
func (b *Boolean) String() string {
	return fmt.Sprintf("BOOLEAN: %t", b.Value)
}

// Encode 编码 BOOLEAN 为 A-XDR 格式
// 根据 DL/T 790.6-2010 标准 6.2 节实现
// 布尔值的A-XDR编码包含长度域和内容域
// 长度域: 0x01 (1字节)
// 内容域: FALSE=0x00, TRUE=0x01
func (b *Boolean) Encode() ([]byte, error) {
	result := make([]byte, 2)

	// 长度域: 1字节
	result[0] = 0x01

	// 内容域
	if b.Value {
		// TRUE: 使用 0x01
		result[1] = 0x01
	} else {
		// FALSE: 使用 0x00
		result[1] = 0x00
	}

	return result, nil
}

// Decode 从 A-XDR 格式解码 BOOLEAN
func (b *Boolean) Decode(data []byte) (int, error) {
	if len(data) < 2 {
		return 0, ErrBufferTooSmall
	}

	// 检查长度域
	if data[0] != 0x01 {
		return 0, fmt.Errorf("%w: invalid boolean length %d, expected 1", ErrInvalidLength, data[0])
	}

	// 解码内容域
	b.Value = data[1] != 0x00

	return 2, nil
}
