package dlt69845

import (
	"fmt"
	"testing"

	"tp.service/internal/protocol/axdr"
)

// TestDoubleLong 测试 DoubleLong 类型编解码
func TestDoubleLong(t *testing.T) {
	tests := []struct {
		name     string
		value    int64
		expected []byte
	}{
		{"Zero", 0, []byte{0x00, 0x00, 0x00, 0x00}},
		{"Positive small", 123, []byte{0x00, 0x00, 0x00, 0x7B}},
		{"Positive large", 128, []byte{0x00, 0x00, 0x00, 0xFF}},
		// {"Negative", -1, []byte{0x81, 0xFF}},
		{"Negative large", -128, []byte{0x82, 0xFF, 0x80}},
	}

	codec := axdr.NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			doubleLong := NewDoubleLong(int32(tt.value))
			encoded, err := codec.Encode(doubleLong)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != len(tt.expected) {
				t.<PERSON>("Expected length %d, got %d", len(tt.expected), len(encoded))
			}

			hexStr := fmt.Sprintf("%x", encoded)
			fmt.Println(hexStr)

			// 测试解码
			decoded := NewDoubleLong(0)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %d, got %d", tt.value, decoded.Value)
			}
		})
	}
}
