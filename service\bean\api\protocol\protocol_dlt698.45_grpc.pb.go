//*
// DL/T 698.45 通信协议服务
// DL/T 698.45 的通信协议组织及解析服务

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/protocol/protocol_dlt698.45.proto

package protocol

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DLT69845ProtSvc_GetSvcInfo_FullMethodName = "/protocol.DLT69845ProtSvc/GetSvcInfo"
	DLT69845ProtSvc_ProtDDO_FullMethodName    = "/protocol.DLT69845ProtSvc/ProtDDO"
	DLT69845ProtSvc_ProtDDP_FullMethodName    = "/protocol.DLT69845ProtSvc/ProtDDP"
	DLT69845ProtSvc_ProtO_FullMethodName      = "/protocol.DLT69845ProtSvc/ProtO"
	DLT69845ProtSvc_ProtP_FullMethodName      = "/protocol.DLT69845ProtSvc/ProtP"
)

// DLT69845ProtSvcClient is the client API for DLT69845ProtSvc service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// *
// 通信协议服务网关
// 对检测服务提供协议报文的组织及解析支持
type DLT69845ProtSvcClient interface {
	// *
	// 获取服务信息 Get Service Info
	// 参数
	//   - 无
	//
	// 返回
	//   - services: 服务信息
	GetSvcInfo(ctx context.Context, in *GetSvcInfoReq, opts ...grpc.CallOption) (*GetSvcInfoResp, error)
	// *
	// 协议数据域组织 Protocol Data Domain Organization
	// 参数
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - datadomain: 组织后的数据, 字符串格式
	ProtDDO(ctx context.Context, in *CPDOReq, opts ...grpc.CallOption) (*CPDOResp, error)
	// *
	// 协议数据域解析 Protocol Data Domain Parsing
	// 参数
	//   - datadomain: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtDDP(ctx context.Context, in *CPDReq, opts ...grpc.CallOption) (*CPDResp, error)
	// *
	// 协议组织 Protocol Organization
	// 参数
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - frame: 组织后的数据, 字符串格式
	ProtO(ctx context.Context, in *CPOReq, opts ...grpc.CallOption) (*CPOResp, error)
	// *
	// 协议解析 Protocol Parsing
	// 参数
	//   - frame: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtP(ctx context.Context, in *CPPReq, opts ...grpc.CallOption) (*CPPResp, error)
}

type dLT69845ProtSvcClient struct {
	cc grpc.ClientConnInterface
}

func NewDLT69845ProtSvcClient(cc grpc.ClientConnInterface) DLT69845ProtSvcClient {
	return &dLT69845ProtSvcClient{cc}
}

func (c *dLT69845ProtSvcClient) GetSvcInfo(ctx context.Context, in *GetSvcInfoReq, opts ...grpc.CallOption) (*GetSvcInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSvcInfoResp)
	err := c.cc.Invoke(ctx, DLT69845ProtSvc_GetSvcInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dLT69845ProtSvcClient) ProtDDO(ctx context.Context, in *CPDOReq, opts ...grpc.CallOption) (*CPDOResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPDOResp)
	err := c.cc.Invoke(ctx, DLT69845ProtSvc_ProtDDO_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dLT69845ProtSvcClient) ProtDDP(ctx context.Context, in *CPDReq, opts ...grpc.CallOption) (*CPDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPDResp)
	err := c.cc.Invoke(ctx, DLT69845ProtSvc_ProtDDP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dLT69845ProtSvcClient) ProtO(ctx context.Context, in *CPOReq, opts ...grpc.CallOption) (*CPOResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPOResp)
	err := c.cc.Invoke(ctx, DLT69845ProtSvc_ProtO_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dLT69845ProtSvcClient) ProtP(ctx context.Context, in *CPPReq, opts ...grpc.CallOption) (*CPPResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPPResp)
	err := c.cc.Invoke(ctx, DLT69845ProtSvc_ProtP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DLT69845ProtSvcServer is the server API for DLT69845ProtSvc service.
// All implementations must embed UnimplementedDLT69845ProtSvcServer
// for forward compatibility.
//
// *
// 通信协议服务网关
// 对检测服务提供协议报文的组织及解析支持
type DLT69845ProtSvcServer interface {
	// *
	// 获取服务信息 Get Service Info
	// 参数
	//   - 无
	//
	// 返回
	//   - services: 服务信息
	GetSvcInfo(context.Context, *GetSvcInfoReq) (*GetSvcInfoResp, error)
	// *
	// 协议数据域组织 Protocol Data Domain Organization
	// 参数
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - datadomain: 组织后的数据, 字符串格式
	ProtDDO(context.Context, *CPDOReq) (*CPDOResp, error)
	// *
	// 协议数据域解析 Protocol Data Domain Parsing
	// 参数
	//   - datadomain: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtDDP(context.Context, *CPDReq) (*CPDResp, error)
	// *
	// 协议组织 Protocol Organization
	// 参数
	//   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
	//     例 Modbus 需要寄存器类型、地址、数据等
	//     例 DL/T 698.45 需要数据结构类型、地址、数据等
	//
	// 返回
	//   - frame: 组织后的数据, 字符串格式
	ProtO(context.Context, *CPOReq) (*CPOResp, error)
	// *
	// 协议解析 Protocol Parsing
	// 参数
	//   - frame: 待解析的数据, 字符串格式
	//
	// 返回
	//   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	ProtP(context.Context, *CPPReq) (*CPPResp, error)
	mustEmbedUnimplementedDLT69845ProtSvcServer()
}

// UnimplementedDLT69845ProtSvcServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDLT69845ProtSvcServer struct{}

func (UnimplementedDLT69845ProtSvcServer) GetSvcInfo(context.Context, *GetSvcInfoReq) (*GetSvcInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSvcInfo not implemented")
}
func (UnimplementedDLT69845ProtSvcServer) ProtDDO(context.Context, *CPDOReq) (*CPDOResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtDDO not implemented")
}
func (UnimplementedDLT69845ProtSvcServer) ProtDDP(context.Context, *CPDReq) (*CPDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtDDP not implemented")
}
func (UnimplementedDLT69845ProtSvcServer) ProtO(context.Context, *CPOReq) (*CPOResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtO not implemented")
}
func (UnimplementedDLT69845ProtSvcServer) ProtP(context.Context, *CPPReq) (*CPPResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtP not implemented")
}
func (UnimplementedDLT69845ProtSvcServer) mustEmbedUnimplementedDLT69845ProtSvcServer() {}
func (UnimplementedDLT69845ProtSvcServer) testEmbeddedByValue()                         {}

// UnsafeDLT69845ProtSvcServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DLT69845ProtSvcServer will
// result in compilation errors.
type UnsafeDLT69845ProtSvcServer interface {
	mustEmbedUnimplementedDLT69845ProtSvcServer()
}

func RegisterDLT69845ProtSvcServer(s grpc.ServiceRegistrar, srv DLT69845ProtSvcServer) {
	// If the following call pancis, it indicates UnimplementedDLT69845ProtSvcServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DLT69845ProtSvc_ServiceDesc, srv)
}

func _DLT69845ProtSvc_GetSvcInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSvcInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DLT69845ProtSvcServer).GetSvcInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DLT69845ProtSvc_GetSvcInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DLT69845ProtSvcServer).GetSvcInfo(ctx, req.(*GetSvcInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DLT69845ProtSvc_ProtDDO_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPDOReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DLT69845ProtSvcServer).ProtDDO(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DLT69845ProtSvc_ProtDDO_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DLT69845ProtSvcServer).ProtDDO(ctx, req.(*CPDOReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DLT69845ProtSvc_ProtDDP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DLT69845ProtSvcServer).ProtDDP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DLT69845ProtSvc_ProtDDP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DLT69845ProtSvcServer).ProtDDP(ctx, req.(*CPDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DLT69845ProtSvc_ProtO_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPOReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DLT69845ProtSvcServer).ProtO(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DLT69845ProtSvc_ProtO_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DLT69845ProtSvcServer).ProtO(ctx, req.(*CPOReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DLT69845ProtSvc_ProtP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPPReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DLT69845ProtSvcServer).ProtP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DLT69845ProtSvc_ProtP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DLT69845ProtSvcServer).ProtP(ctx, req.(*CPPReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DLT69845ProtSvc_ServiceDesc is the grpc.ServiceDesc for DLT69845ProtSvc service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DLT69845ProtSvc_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "protocol.DLT69845ProtSvc",
	HandlerType: (*DLT69845ProtSvcServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSvcInfo",
			Handler:    _DLT69845ProtSvc_GetSvcInfo_Handler,
		},
		{
			MethodName: "ProtDDO",
			Handler:    _DLT69845ProtSvc_ProtDDO_Handler,
		},
		{
			MethodName: "ProtDDP",
			Handler:    _DLT69845ProtSvc_ProtDDP_Handler,
		},
		{
			MethodName: "ProtO",
			Handler:    _DLT69845ProtSvc_ProtO_Handler,
		},
		{
			MethodName: "ProtP",
			Handler:    _DLT69845ProtSvc_ProtP_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/protocol/protocol_dlt698.45.proto",
}
