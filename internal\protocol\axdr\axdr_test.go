package axdr

import (
	"fmt"
	"testing"
	"time"
)

// TestInteger 测试 INTEGER 类型编解码
func TestInteger(t *testing.T) {
	tests := []struct {
		name     string
		value    int64
		expected []byte
	}{
		{"Zero", 0, []byte{0x00}},
		{"Positive small", 123, []byte{0x7B}},
		{"Positive large", 128, []byte{0x82, 0x00, 0x80}},
		{"Negative", -1, []byte{0x81, 0xFF}},
		{"Negative large", -128, []byte{0x82, 0xFF, 0x80}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			integer := NewInteger(tt.value)
			encoded, err := codec.Encode(integer)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != len(tt.expected) {
				t.Errorf("Expected length %d, got %d", len(tt.expected), len(encoded))
			}

			// 测试解码
			decoded := NewInteger(0)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %d, got %d", tt.value, decoded.Value)
			}
		})
	}
}

// TestConstrainedInteger 测试受约束的 INTEGER
func TestConstrainedInteger(t *testing.T) {
	tests := []struct {
		name     string
		value    int64
		min      int64
		max      int64
		expected []byte
	}{
		{"UInt8", 255, 0, 255, []byte{0xFF}},
		{"UInt16", 65535, 0, 65535, []byte{0xFF, 0xFF}},
		{"Int8", -128, -128, 127, []byte{0x80}},
		{"Int16", -32768, -32768, 32767, []byte{0x80, 0x00}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			integer := NewConstrainedInteger(tt.value, tt.min, tt.max)
			encoded, err := codec.Encode(integer)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != len(tt.expected) {
				t.Errorf("Expected length %d, got %d", len(tt.expected), len(encoded))
			}

			// 测试解码
			decoded := NewConstrainedInteger(0, tt.min, tt.max)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %d, got %d", tt.value, decoded.Value)
			}
		})
	}
}

// TestBoolean 测试 BOOLEAN 类型编解码
func TestBoolean(t *testing.T) {
	tests := []struct {
		name     string
		value    bool
		expected []byte
	}{
		{"False", false, []byte{0x00}},
		{"True", true, []byte{0xFF}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			boolean := NewBoolean(tt.value)
			encoded, err := codec.Encode(boolean)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != 1 {
				t.Errorf("Expected length 1, got %d", len(encoded))
			}

			// 测试解码
			decoded := NewBoolean(false)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != 1 {
				t.Errorf("Expected consumed 1, got %d", consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %t, got %t", tt.value, decoded.Value)
			}
		})
	}
}

// TestEnumerated 测试 ENUMERATED 类型编解码
func TestEnumerated(t *testing.T) {
	tests := []struct {
		name     string
		value    byte
		expected []byte
	}{
		{"Zero", 0, []byte{0x00}},
		{"Max", 255, []byte{0xFF}},
		{"Middle", 128, []byte{0x80}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			enumerated := NewEnumerated(tt.value)
			encoded, err := codec.Encode(enumerated)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			if len(encoded) != 1 {
				t.Errorf("Expected length 1, got %d", len(encoded))
			}

			if encoded[0] != tt.expected[0] {
				t.Errorf("Expected %02X, got %02X", tt.expected[0], encoded[0])
			}

			// 测试解码
			decoded := NewEnumerated(0)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != 1 {
				t.Errorf("Expected consumed 1, got %d", consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %d, got %d", tt.value, decoded.Value)
			}
		})
	}
}

// TestBitString 测试 BIT STRING 类型编解码
func TestBitString(t *testing.T) {
	tests := []struct {
		name      string
		value     []byte
		bitLength int
		expected  []byte
	}{
		{"Empty", []byte{}, 0, []byte{0x00}},
		{"Single bit", []byte{0x80}, 1, []byte{0x01, 0x80}},
		{"Full byte", []byte{0xFF}, 8, []byte{0x08, 0xFF}},
		{"Partial byte", []byte{0xE0}, 3, []byte{0x03, 0xE0}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			bitString := NewBitString(tt.value, tt.bitLength)
			encoded, err := codec.Encode(bitString)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			// 测试解码
			decoded := NewBitString(nil, 0)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.BitLength != tt.bitLength {
				t.Errorf("Expected bit length %d, got %d", tt.bitLength, decoded.BitLength)
			}
		})
	}
}

// TestByteString 测试 BYTE STRING 类型编解码
func TestByteString(t *testing.T) {
	tests := []struct {
		name     string
		value    []byte
		expected []byte
	}{
		{"Empty", []byte{}, []byte{0x00}},
		{"Single byte", []byte{0x42}, []byte{0x01, 0x42}},
		{"Multiple bytes", []byte{0x41, 0x42, 0x43, 0x44}, []byte{0x04, 0x41, 0x42, 0x43, 0x44}},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			byteString := NewByteString(tt.value)
			encoded, err := codec.Encode(byteString)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			// 测试解码
			decoded := NewByteString(nil)
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if len(decoded.Value) != len(tt.value) {
				t.Errorf("Expected length %d, got %d", len(tt.value), len(decoded.Value))
			}

			for i, b := range tt.value {
				if decoded.Value[i] != b {
					t.Errorf("Expected byte[%d] %02X, got %02X", i, b, decoded.Value[i])
				}
			}
		})
	}
}

// TestVisibleString 测试 VisibleString 类型编解码
func TestVisibleString(t *testing.T) {
	tests := []struct {
		name  string
		value string
	}{
		{"Empty", ""},
		{"Simple", "Hello"},
		{"With spaces", "Hello World"},
		{"Numbers", "12345"},
		{"Special chars", "!@#$%^&*()"},
	}

	codec := NewCodec()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			visibleString := NewVisibleString(tt.value)
			encoded, err := codec.Encode(visibleString)
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			// 测试解码
			decoded := NewVisibleString("")
			consumed, err := codec.Decode(encoded, decoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			if consumed != len(encoded) {
				t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
			}

			if decoded.Value != tt.value {
				t.Errorf("Expected value %q, got %q", tt.value, decoded.Value)
			}
		})
	}
}

// TestGeneralizedTime 测试 GeneralizedTime 类型编解码
func TestGeneralizedTime(t *testing.T) {
	testTime := time.Date(2023, 12, 25, 15, 30, 45, 0, time.UTC)

	codec := NewCodec()

	// 测试编码
	generalizedTime := NewGeneralizedTime(testTime)
	encoded, err := codec.Encode(generalizedTime)
	if err != nil {
		t.Fatalf("Encode failed: %v", err)
	}

	// 测试解码
	decoded := NewGeneralizedTime(time.Time{})
	consumed, err := codec.Decode(encoded, decoded)
	if err != nil {
		t.Fatalf("Decode failed: %v", err)
	}

	if consumed != len(encoded) {
		t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
	}

	if !decoded.Value.Equal(testTime) {
		t.Errorf("Expected time %v, got %v", testTime, decoded.Value)
	}
}

// TestNull 测试 NULL 类型编解码
func TestNull(t *testing.T) {
	codec := NewCodec()

	// 测试编码
	null := NewNull()
	encoded, err := codec.Encode(null)
	if err != nil {
		t.Fatalf("Encode failed: %v", err)
	}

	if len(encoded) != 0 {
		t.Errorf("Expected empty encoding, got %d bytes", len(encoded))
	}

	// 测试解码
	decoded := NewNull()
	consumed, err := codec.Decode(encoded, decoded)
	if err != nil {
		t.Fatalf("Decode failed: %v", err)
	}

	if consumed != 0 {
		t.Errorf("Expected consumed 0, got %d", consumed)
	}
}

// DummyPDU 表示 ASN.1 定义的 Dummy_PDU 结构
//
//	Dummy_PDU ::= SEQUENCE {
//	    a       INTEGER(0..127),
//	    b       OCTET STRING(SIZE(4)) OPTIONAL,
//	    c       [1] BOOLEAN DEFAULT TRUE,
//	}
type DummyPDU struct {
	A *Integer    // INTEGER(0..127)
	B *ByteString // OCTET STRING(SIZE(4)) OPTIONAL
	C *Boolean    // [1] BOOLEAN DEFAULT TRUE
}

// NewDummyPDU 创建新的 DummyPDU 实例
func NewDummyPDU(a int64, b []byte, c bool) *DummyPDU {
	// 创建受约束的整数 a (0..127)
	intA := NewConstrainedInteger(a, 0, 127)

	// 创建可选的字节串 b (SIZE(4))
	var byteB *ByteString
	if b != nil {
		byteB = NewConstrainedByteString(b, 4)
	}

	// 创建布尔值 c (默认为 true)
	boolC := NewBoolean(c)

	return &DummyPDU{
		A: intA,
		B: byteB,
		C: boolC,
	}
}

// ToSequence 将 DummyPDU 转换为 AXDR Sequence
func (d *DummyPDU) ToSequence() *Sequence {
	elements := []SequenceElement{
		// a: INTEGER(0..127) - 必需字段
		{Value: d.A},
	}

	// b: OCTET STRING(SIZE(4)) OPTIONAL - 可选字段
	// 根据 ASN.1 OPTIONAL 规则：只有当字段存在时才编码
	if d.B != nil {
		elements = append(elements, SequenceElement{
			Optional: NewOptional(true, d.B),
		})
	}
	// 如果字段不存在，则不添加任何元素到序列中

	// c: [1] BOOLEAN DEFAULT TRUE - 默认值字段
	// 根据 ASN.1 规则：
	// - 当值不等于默认值时：编码存在标记 + 实际值
	// - 当值等于默认值时：只编码存在标记，不编码值
	defaultValue := NewBoolean(true) // 默认值是 TRUE
	if d.C.Value != defaultValue.Value {
		// 值不等于默认值，编码存在标记和实际值
		elements = append(elements, SequenceElement{
			Default: NewDefault(true, d.C, defaultValue),
		})
	} else {
		// 值等于默认值，只编码存在标记，不编码值
		elements = append(elements, SequenceElement{
			Default: NewDefault(true, nil, defaultValue),
		})
	}

	return NewSequence(elements)
}

// TestSequence 测试 SEQUENCE 类型编解码
func TestSequence(t *testing.T) {
	// 测试简单的序列
	t.Run("Simple Sequence", func(t *testing.T) {
		serial1 := NewInteger(37)
		serial1.IsConstrained = true
		serial1.ByteLength = 1
		serial1.MinValue = 0
		serial1.MaxValue = 127

		serial2 := NewByteString([]byte("ABCF"))
		serial2.IsConstrained = true
		serial2.Size = 4

		serial3 := NewBoolean(false)

		sequence := NewSequence([]SequenceElement{
			{Value: serial1},
			{Value: serial2},
			{Value: serial3},
		})

		codec := NewCodec()

		// 测试编码
		encoded, err := codec.Encode(sequence)
		if err != nil {
			t.Fatalf("Encode failed: %v", err)
		}

		fmt.Println("Simple Sequence:", FormatHex(encoded))

		// 测试解码
		decodedInt := NewInteger(0)
		decodedInt.IsConstrained = true
		decodedInt.ByteLength = 1
		decodedInt.MinValue = 0
		decodedInt.MaxValue = 127

		decodedBytes := NewByteString(nil)
		decodedBytes.IsConstrained = true
		decodedBytes.Size = 4

		decoded := NewSequence([]SequenceElement{
			{Value: decodedInt},
			{Value: decodedBytes},
			{Value: NewBoolean(false)},
		})
		consumed, err := codec.Decode(encoded, decoded)
		if err != nil {
			t.Fatalf("Decode failed: %v", err)
		}

		if consumed != len(encoded) {
			t.Errorf("Expected consumed %d, got %d", len(encoded), consumed)
		}
	})

	// 测试 DummyPDU 结构
	t.Run("DummyPDU", func(t *testing.T) {
		// 创建 DummyPDU 实例 - 包含所有字段
		dummyPDU1 := NewDummyPDU(37, []byte("ABCD"), false)
		sequence1 := dummyPDU1.ToSequence()

		codec := NewCodec()

		// 测试编码
		encoded1, err := codec.Encode(sequence1)
		if err != nil {
			t.Fatalf("DummyPDU encode failed: %v", err)
		}

		fmt.Println("DummyPDU with all fields:", FormatHex(encoded1))

		// 验证编码结果 - DEFAULT 值不等于默认值时编码存在标记+完整值
		expected := []byte{0x25, 0x01, 0x41, 0x42, 0x43, 0x44, 0x01, 0x01, 0x00}
		if len(encoded1) != len(expected) {
			t.Errorf("Expected length %d, got %d", len(expected), len(encoded1))
		} else {
			for i, b := range expected {
				if encoded1[i] != b {
					t.Errorf("Byte %d: expected 0x%02X, got 0x%02X", i, b, encoded1[i])
				}
			}
			t.Log("✓ 编码结果完全正确！")
		}

		// 创建 DummyPDU 实例 - 不包含可选字段
		dummyPDU2 := NewDummyPDU(37, nil, false)
		sequence2 := dummyPDU2.ToSequence()

		// 测试编码
		encoded2, err := codec.Encode(sequence2)
		if err != nil {
			t.Fatalf("DummyPDU encode failed: %v", err)
		}

		fmt.Println("DummyPDU without optional field:", FormatHex(encoded2))

		// 验证编码结果 - OPTIONAL 字段不存在时不编码任何内容
		// NewDummyPDU(37, nil, false): 37=0x25, nil=不编码, false≠默认值TRUE
		expected2 := []byte{0x25, 0x01, 0x01, 0x00}
		if len(encoded2) != len(expected2) {
			t.Errorf("Expected length %d, got %d", len(expected2), len(encoded2))
		} else {
			for i, b := range expected2 {
				if encoded2[i] != b {
					t.Errorf("Byte %d: expected 0x%02X, got 0x%02X", i, b, encoded2[i])
				}
			}
			t.Log("✓ OPTIONAL 字段不存在时编码结果完全正确！")
		}

		// 创建 DummyPDU 实例 - 不包含可选字段
		dummyPDU3 := NewDummyPDU(37, []byte("ABCD"), true)
		sequence3 := dummyPDU3.ToSequence()

		// 测试编码
		encoded3, err := codec.Encode(sequence3)
		if err != nil {
			t.Fatalf("DummyPDU encode failed: %v", err)
		}

		fmt.Println("DummyPDU with all fields:", FormatHex(encoded3))

		// 验证编码结果 - DEFAULT 值等于默认值时只编码存在标记
		expected3 := []byte{0x25, 0x01, 0x41, 0x42, 0x43, 0x44, 0x01}
		if len(encoded3) != len(expected3) {
			t.Errorf("Expected length %d, got %d", len(expected3), len(encoded3))
		} else {
			for i, b := range expected3 {
				if encoded3[i] != b {
					t.Errorf("Byte %d: expected 0x%02X, got 0x%02X", i, b, encoded3[i])
				}
			}
			t.Log("✓ DEFAULT 值编码结果完全正确！")
		}
	})
}
