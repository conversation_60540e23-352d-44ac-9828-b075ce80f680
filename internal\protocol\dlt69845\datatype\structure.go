package datatype

/**
 * 帧起始字符
 */
type FSC byte

/**
 * 帧长度域类型
 * Unit 单位 -> 0 单位为字节，1 单位为千字节
 * Value 值
 */
type FLD struct {
	Unit  byte
	Value [2]byte
}

/**
 * 帧控制域类型
 * Direction 传输方向 -> 0 客户机发往服务器，1 服务器发往客户机
 * StartFlag 启动标志 -> 0 服务器发起，1 客户机发起
 * FDFlag 分帧标志 -> 0 完整的 APDU，1 APDU 片段
 * SCFlag 扰码标志 -> 0 链路用户数据不加扰码，1 链路用户数据加扰码（按字节加 33H）
 * FuncCode 功能码 -> 1 链路管理（登录、心跳、退出登录），3 用户数据（应用连接管理及数据交换服务）
 */
type FCD struct {
	Direction byte
	StartFlag byte
	FDFlag    byte
	SCFlag    byte
	FuncCode  byte
}

/**
 * 帧地址域类型
 * SA 服务器地址
 * 	- Type 地址类型 -> 0 单地址，1 通配地址，2 组地址，3 广播地址
 * 	- LAFlag 逻辑扩展地址标志 -> 0 无逻辑扩展地址（逻辑地址 0），1 无逻辑扩展地址（逻辑地址 1），2 有逻辑扩展地址
 * 	- Len 地址长度 -> 0 ~ 15 表示 1 ~ 16 个字节长度
 * 	- Addr 地址 -> 有扩展逻辑地址时第一字节为逻辑地址
 * CA 客户端地址
 */
type FAD struct {
	SA struct {
		Type   byte
		LAFlag byte
		Len    byte
		Addr   []byte
	}
	CA byte
}

/**
 * 帧头校验
 */
type HCS [2]byte

/**
 * 链路用户数据
 */
// type APDU struct{}

// type LinkAPDU APDU

// type ClientAPDU APDU

/**
 * 帧校验符
 */
type FCS [2]byte

/**
 * 帧结束符
 */
type FEC byte
