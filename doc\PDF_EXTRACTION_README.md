# DL/T 790.6-2010 PDF 内容提取工具

本文档介绍如何使用 Golang 提取 DL/T 790.6-2010 标准 PDF 文档内容，并将其存储在项目中供代码使用。

## 概述

DL/T 790.6-2010 是《采用配电线载波的配电自动化 第6部分: A-XDR编码规则》标准。本工具将 PDF 文档内容提取为 Go 代码，方便在程序中直接访问标准内容。

## 文件结构

```
├── cmd/
│   ├── pdf_extractor/           # 基础 PDF 提取工具
│   │   └── main.go
│   ├── pdf_extractor_advanced/  # 高级 PDF 提取工具 (需要许可证)
│   │   └── main.go
│   └── demo_dlt790_6/          # 演示程序
│       └── main.go
├── internal/protocol/dlt69845/datatype/
│   ├── dlt790_6_extracted_content.go  # 提取的内容
│   ├── dlt790_6_usage_example.go      # 使用示例
│   └── dlt790_6_test.go               # 测试文件
├── scripts/
│   └── extract_pdf.sh          # 提取脚本
└── doc/
    ├── DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf
    └── PDF_EXTRACTION_README.md
```

## 使用方法

### 1. 运行 PDF 提取工具

#### 方法一：使用脚本（推荐）
```bash
# 在项目根目录运行
./scripts/extract_pdf.sh
```

#### 方法二：直接运行 Go 程序
```bash
# 基础提取工具
go run cmd/pdf_extractor/main.go

# 高级提取工具（需要 unipdf 许可证）
go run cmd/pdf_extractor_advanced/main.go
```

### 2. 在代码中使用提取的内容

```go
package main

import (
    "fmt"
    "tp.service/internal/protocol/dlt69845/datatype"
)

func main() {
    // 获取完整内容
    content := datatype.GetDLT790_6_Content()
    fmt.Printf("标准文档总字符数: %d\n", len(content))

    // 获取特定章节
    if extractedAt, exists := datatype.GetDLT790_6_Section("extracted_at"); exists {
        fmt.Printf("提取时间: %s\n", extractedAt)
    }

    // 使用高级功能
    usage := datatype.NewDLT790_6_Usage()
    
    // 搜索内容
    results := usage.SearchContent("A-XDR")
    fmt.Printf("找到 %d 个 A-XDR 相关条目\n", len(results))
    
    // 获取特定页面内容
    page1 := usage.GetPageContent(1)
    fmt.Printf("第1页内容长度: %d\n", len(page1))
}
```

### 3. 运行演示程序

```bash
# 完整演示
go run cmd/demo_dlt790_6/main.go

# 搜索特定关键词
go run cmd/demo_dlt790_6/main.go search A-XDR
go run cmd/demo_dlt790_6/main.go search 编码

# 查看特定页面
go run cmd/demo_dlt790_6/main.go page 1
go run cmd/demo_dlt790_6/main.go page 5

# 显示帮助
go run cmd/demo_dlt790_6/main.go help
```

## API 参考

### 基础函数

#### `GetDLT790_6_Content() string`
返回完整的提取内容。

#### `GetDLT790_6_Section(section string) (string, bool)`
获取特定章节内容。可用的章节：
- `"full_content"`: 完整内容
- `"extracted_at"`: 提取时间
- `"source_file"`: 源文件路径

### 高级功能类

#### `NewDLT790_6_Usage() *DLT790_6_Usage`
创建使用示例实例。

#### `SearchContent(searchTerm string) []string`
搜索包含指定关键词的行。

#### `GetPageContent(pageNum int) string`
获取指定页面的内容。

#### `FindAXDRRules() []string`
查找 A-XDR 编码规则相关内容。

#### `GetEncodingExamples() []string`
提取编码示例。

#### `PrintStatistics()`
打印内容统计信息。

## 测试

运行测试以验证提取的内容：

```bash
# 运行所有测试
go test -v ./internal/protocol/dlt69845/datatype/

# 运行特定测试
go test -v ./internal/protocol/dlt69845/datatype/ -run TestDLT790_6_Content

# 运行基准测试
go test -bench=. ./internal/protocol/dlt69845/datatype/
```

## 依赖

项目使用以下 Go 包进行 PDF 处理：

- `github.com/ledongthuc/pdf`: 基础 PDF 文本提取（免费）
- `github.com/unidoc/unipdf/v3`: 高级 PDF 处理（需要许可证）

## 提取内容统计

- **总页数**: 24 页
- **总字符数**: 47,723 字符
- **A-XDR 相关条目**: 25 个
- **编码规则条目**: 132 个

## 包含的编码类型

提取的内容包含以下 A-XDR 编码类型的详细说明：

- INTEGER（整型）
- BOOLEAN（布尔型）
- ENUMERATED（枚举型）
- BIT STRING（位串）
- BYTE STRING（字节串）
- SEQUENCE（序列）
- CHOICE（选择）
- VisibleString（可视串）
- GeneralizedTime（通用时间）
- NULL（空值）

## 注意事项

1. **许可证要求**: `unipdf` 库需要商业许可证才能在生产环境中使用。基础提取工具使用免费的 `ledongthuc/pdf` 库。

2. **内容准确性**: 提取的内容可能包含 OCR 错误或格式问题。建议在使用前进行验证。

3. **编码格式**: 提取的内容以 UTF-8 编码存储，支持中文字符。

4. **更新**: 如果 PDF 文件更新，需要重新运行提取工具。

## 故障排除

### 常见问题

1. **PDF 文件不存在**
   ```
   错误: PDF 文件不存在: doc/DL T 790.6-2010...
   ```
   确保 PDF 文件位于正确的路径。

2. **依赖缺失**
   ```
   no required module provides package github.com/ledongthuc/pdf
   ```
   运行 `go mod tidy` 安装依赖。

3. **unipdf 许可证错误**
   ```
   unipdf license code required
   ```
   使用基础提取工具或获取 unipdf 许可证。

### 重新提取

如果需要重新提取内容：

```bash
# 删除旧的提取文件
rm internal/protocol/dlt69845/datatype/dlt790_6_extracted_content.go

# 重新运行提取工具
go run cmd/pdf_extractor/main.go
```

## 贡献

如果发现提取内容有误或需要改进，请：

1. 检查原始 PDF 文档
2. 修改提取工具的逻辑
3. 重新运行提取和测试
4. 提交更改

## 许可证

本工具遵循项目的整体许可证。PDF 内容版权归原标准制定方所有。
