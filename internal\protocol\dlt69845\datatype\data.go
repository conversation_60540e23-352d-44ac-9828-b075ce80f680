package datatype

import (
	"fmt"

	"tp.service/internal/protocol/axdr"
)

// Data 是 DL/T 698.45 协议中的核心数据类型
// 它是一个 CHOICE 类型，包含了协议中所有可能的数据类型
type Data struct {
	*axdr.Choice
}

// Type 返回 Data 的 A-XDR 类型
func (d *Data) Type() axdr.AXDRType {
	return axdr.TypeChoice
}

// Encode 编码 Data 为 A-XDR 格式
func (d *Data) Encode() ([]byte, error) {
	return d.Choice.Encode()
}

// Decode 从 A-XDR 格式解码 Data
func (d *Data) Decode(data []byte) (int, error) {
	// 创建类型映射
	typeMap := d.createTypeMap()
	return d.Choice.DecodeWithTypes(data, typeMap)
}

// createTypeMap 创建 Data 类型的类型映射
func (d *Data) createTypeMap() map[byte]func() axdr.AXDRValue {
	return map[byte]func() axdr.AXDRValue{
		DataTypeNull:               func() axdr.AXDRValue { return axdr.NewNull() },
		DataTypeBool:               func() axdr.AXDRValue { return axdr.NewBoolean(false) },
		DataTypeBitString:          func() axdr.AXDRValue { return axdr.NewBitString(nil, 0) },
		DataTypeDoubleLong:         func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeDoubleLongUnsigned: func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeOctetString:        func() axdr.AXDRValue { return axdr.NewByteString(nil) },
		DataTypeVisibleString:      func() axdr.AXDRValue { return axdr.NewVisibleString("") },
		DataTypeUTF8String:         func() axdr.AXDRValue { return axdr.NewVisibleString("") },
		DataTypeInteger:            func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeLong:               func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeUnsigned:           func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeLongUnsigned:       func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeLong64:             func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeLong64Unsigned:     func() axdr.AXDRValue { return axdr.NewInteger(0) },
		DataTypeEnum:               func() axdr.AXDRValue { return axdr.NewEnumerated(0) },
		DataTypeFloat32:            func() axdr.AXDRValue { return axdr.NewInteger(0) }, // 暂时用整数代替
		DataTypeFloat64:            func() axdr.AXDRValue { return axdr.NewInteger(0) }, // 暂时用整数代替
		DataTypeOI:                 func() axdr.AXDRValue { return axdr.NewInteger(0) },
		// 其他复杂类型暂时不实现
	}
}

// NewData 创建一个新的 Data 实例
func NewData() *Data {
	return &Data{
		Choice: &axdr.Choice{},
	}
}

// NewDataWithValue 创建一个包含指定值的 Data 实例
func NewDataWithValue(tag byte, value axdr.AXDRValue) *Data {
	return &Data{
		Choice: axdr.NewChoice(tag, value),
	}
}

// 便捷构造函数

// NewDataNull 创建 NULL 类型的 Data
func NewDataNull() *Data {
	return NewDataWithValue(DataTypeNull, axdr.NewNull())
}

// NewDataBool 创建 bool 类型的 Data
func NewDataBool(value bool) *Data {
	return NewDataWithValue(DataTypeBool, axdr.NewBoolean(value))
}

// NewDataBitString 创建 bit-string 类型的 Data
func NewDataBitString(bits []bool) *Data {
	// 将 bool 数组转换为字节数组
	byteLength := (len(bits) + 7) / 8
	value := make([]byte, byteLength)

	for i, bit := range bits {
		if bit {
			byteIndex := i / 8
			bitIndex := 7 - (i % 8) // 大端序位排列
			value[byteIndex] |= 1 << bitIndex
		}
	}

	return NewDataWithValue(DataTypeBitString, axdr.NewBitString(value, len(bits)))
}

// NewDataDoubleLong 创建 double-long 类型的 Data
func NewDataDoubleLong(value int32) *Data {
	return NewDataWithValue(DataTypeDoubleLong, axdr.NewInteger(int64(value)))
}

// NewDataDoubleLongUnsigned 创建 double-long-unsigned 类型的 Data
func NewDataDoubleLongUnsigned(value uint32) *Data {
	return NewDataWithValue(DataTypeDoubleLongUnsigned, axdr.NewInteger(int64(value)))
}

// NewDataOctetString 创建 octet-string 类型的 Data
func NewDataOctetString(value []byte) *Data {
	return NewDataWithValue(DataTypeOctetString, axdr.NewByteString(value))
}

// NewDataVisibleString 创建 visible-string 类型的 Data
func NewDataVisibleString(value string) *Data {
	return NewDataWithValue(DataTypeVisibleString, axdr.NewVisibleString(value))
}

// NewDataUTF8String 创建 UTF8-string 类型的 Data
func NewDataUTF8String(value string) *Data {
	return NewDataWithValue(DataTypeUTF8String, axdr.NewVisibleString(value))
}

// NewDataInteger 创建 integer 类型的 Data
func NewDataInteger(value int8) *Data {
	return NewDataWithValue(DataTypeInteger, axdr.NewInteger(int64(value)))
}

// NewDataLong 创建 long 类型的 Data
func NewDataLong(value int16) *Data {
	return NewDataWithValue(DataTypeLong, axdr.NewInteger(int64(value)))
}

// NewDataUnsigned 创建 unsigned 类型的 Data
func NewDataUnsigned(value uint8) *Data {
	return NewDataWithValue(DataTypeUnsigned, axdr.NewInteger(int64(value)))
}

// NewDataLongUnsigned 创建 long-unsigned 类型的 Data
func NewDataLongUnsigned(value uint16) *Data {
	return NewDataWithValue(DataTypeLongUnsigned, axdr.NewInteger(int64(value)))
}

// NewDataLong64 创建 long64 类型的 Data
func NewDataLong64(value int64) *Data {
	return NewDataWithValue(DataTypeLong64, axdr.NewInteger(value))
}

// NewDataLong64Unsigned 创建 long64-unsigned 类型的 Data
func NewDataLong64Unsigned(value uint64) *Data {
	return NewDataWithValue(DataTypeLong64Unsigned, axdr.NewInteger(int64(value)))
}

// NewDataEnum 创建 enum 类型的 Data
func NewDataEnum(value uint8) *Data {
	return NewDataWithValue(DataTypeEnum, axdr.NewEnumerated(value))
}

// NewDataOI 创建 OI 类型的 Data
func NewDataOI(value uint16) *Data {
	return NewDataWithValue(DataTypeOI, axdr.NewInteger(int64(value)))
}

// 类型检查方法

// IsNull 检查是否为 NULL 类型
func (d *Data) IsNull() bool {
	return d.Tag == DataTypeNull
}

// IsBool 检查是否为 bool 类型
func (d *Data) IsBool() bool {
	return d.Tag == DataTypeBool
}

// IsInteger 检查是否为整数类型（包括各种整数变体）
func (d *Data) IsInteger() bool {
	switch d.Tag {
	case DataTypeInteger, DataTypeLong, DataTypeUnsigned, DataTypeLongUnsigned,
		DataTypeDoubleLong, DataTypeDoubleLongUnsigned, DataTypeLong64, DataTypeLong64Unsigned:
		return true
	default:
		return false
	}
}

// IsString 检查是否为字符串类型
func (d *Data) IsString() bool {
	return d.Tag == DataTypeVisibleString || d.Tag == DataTypeUTF8String
}

// 值获取方法

// GetBool 获取 bool 值
func (d *Data) GetBool() (bool, error) {
	if !d.IsBool() {
		return false, fmt.Errorf("data type is not bool, got tag %d", d.Tag)
	}
	if boolean, ok := d.Value.(*axdr.Boolean); ok {
		return boolean.Value, nil
	}
	return false, fmt.Errorf("invalid bool value type")
}

// GetInteger 获取整数值（统一返回 int64）
func (d *Data) GetInteger() (int64, error) {
	if !d.IsInteger() {
		return 0, fmt.Errorf("data type is not integer, got tag %d", d.Tag)
	}
	if integer, ok := d.Value.(*axdr.Integer); ok {
		return integer.Value, nil
	}
	return 0, fmt.Errorf("invalid integer value type")
}

// GetString 获取字符串值
func (d *Data) GetString() (string, error) {
	if !d.IsString() {
		return "", fmt.Errorf("data type is not string, got tag %d", d.Tag)
	}
	if str, ok := d.Value.(*axdr.VisibleString); ok {
		return str.Value, nil
	}
	return "", fmt.Errorf("invalid string value type")
}

// GetOctetString 获取字节串值
func (d *Data) GetOctetString() ([]byte, error) {
	if d.Tag != DataTypeOctetString {
		return nil, fmt.Errorf("data type is not octet-string, got tag %d", d.Tag)
	}
	if byteStr, ok := d.Value.(*axdr.ByteString); ok {
		return byteStr.Value, nil
	}
	return nil, fmt.Errorf("invalid octet-string value type")
}

// GetBitString 获取位串值
func (d *Data) GetBitString() ([]bool, error) {
	if d.Tag != DataTypeBitString {
		return nil, fmt.Errorf("data type is not bit-string, got tag %d", d.Tag)
	}
	if bitStr, ok := d.Value.(*axdr.BitString); ok {
		// 将字节数组转换为 bool 数组
		bits := make([]bool, bitStr.BitLength)
		for i := 0; i < bitStr.BitLength; i++ {
			byteIndex := i / 8
			bitIndex := 7 - (i % 8) // 大端序位排列
			if byteIndex < len(bitStr.Value) {
				bits[i] = (bitStr.Value[byteIndex] & (1 << bitIndex)) != 0
			}
		}
		return bits, nil
	}
	return nil, fmt.Errorf("invalid bit-string value type")
}

// String 返回 Data 的字符串表示
func (d *Data) String() string {
	typeName := d.GetTypeName()
	if d.Value == nil {
		return fmt.Sprintf("Data[%s]: <nil>", typeName)
	}
	return fmt.Sprintf("Data[%s]: %s", typeName, d.Value.String())
}

// GetTypeName 获取类型名称
func (d *Data) GetTypeName() string {
	switch d.Tag {
	case DataTypeNull:
		return "NULL"
	case DataTypeArray:
		return "array"
	case DataTypeStructure:
		return "structure"
	case DataTypeBool:
		return "bool"
	case DataTypeBitString:
		return "bit-string"
	case DataTypeDoubleLong:
		return "double-long"
	case DataTypeDoubleLongUnsigned:
		return "double-long-unsigned"
	case DataTypeOctetString:
		return "octet-string"
	case DataTypeVisibleString:
		return "visible-string"
	case DataTypeUTF8String:
		return "UTF8-string"
	case DataTypeInteger:
		return "integer"
	case DataTypeLong:
		return "long"
	case DataTypeUnsigned:
		return "unsigned"
	case DataTypeLongUnsigned:
		return "long-unsigned"
	case DataTypeLong64:
		return "long64"
	case DataTypeLong64Unsigned:
		return "long64-unsigned"
	case DataTypeEnum:
		return "enum"
	case DataTypeFloat32:
		return "float32"
	case DataTypeFloat64:
		return "float64"
	case DataTypeDateTime:
		return "date_time"
	case DataTypeDate:
		return "date"
	case DataTypeTime:
		return "time"
	case DataTypeDateTimeS:
		return "date_time_s"
	case DataTypeOI:
		return "OI"
	case DataTypeOAD:
		return "OAD"
	case DataTypeROAD:
		return "ROAD"
	case DataTypeOMD:
		return "OMD"
	case DataTypeTI:
		return "TI"
	case DataTypeTSA:
		return "TSA"
	case DataTypeMAC:
		return "MAC"
	case DataTypeRN:
		return "RN"
	case DataTypeRegion:
		return "Region"
	case DataTypeScalerUnit:
		return "Scaler_Unit"
	case DataTypeRSD:
		return "RSD"
	case DataTypeCSD:
		return "CSD"
	case DataTypeMS:
		return "MS"
	case DataTypeSID:
		return "SID"
	case DataTypeSIDMAC:
		return "SID_MAC"
	case DataTypeCOMDCB:
		return "COMDCB"
	case DataTypeRCSD:
		return "RCSD"
	case DataTypeVQDS:
		return "VQDS"
	default:
		return fmt.Sprintf("Unknown(%d)", d.Tag)
	}
}
