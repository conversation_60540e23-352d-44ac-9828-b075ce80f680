package common

import (
	"time"
)

type Common interface {
	// Connect 连接设备
	Connect() error

	// Disconnect 断开连接
	Disconnect() error

	// IsConnected 检查是否已连接
	IsConnected() bool

	// 设置超时时间
	SetTimeout(timeout time.Duration) error

	// 获取超时时间
	GetTimeout() time.Duration

	// 发送数据（发送一次）
	// data: 发送的数据
	// retry: 重试次数，小于等于0表示不重试
	Send(data []byte, retry int) error

	// 接收数据（接收一次）
	// timeout: 超时时间，小于等于0表示使用默认超时
	Receive(timeout time.Duration) ([]byte, error)

	// 接收数据（持续接收至接收到停止接收命令）
	// channel: 用于接收数据的通道
	ReceiveStream(channel chan []byte) error

	// 停止接收数据
	ReceiveSop() error

	// 发送并接收数据
	SendAndReceive(data []byte, timeout time.Duration) ([]byte, error)
}
