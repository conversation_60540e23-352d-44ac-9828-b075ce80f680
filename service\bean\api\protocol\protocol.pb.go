//*
// 通信协议服务网关
// 根据请求中的协议类型重定向到指定服务

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/protocol/protocol.proto

package protocol

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务信息
type ProtServiceInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务名称，每个协议需求分析时确定，不可重复（可用协议类型的 string 格式进行替代）
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 协议版本，用于协议升级时的兼容性判断
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// 协议描述，用于展示给用户，描述协议的功能、用途等信息
	Description   string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtServiceInfo) Reset() {
	*x = ProtServiceInfo{}
	mi := &file_api_protocol_protocol_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtServiceInfo) ProtoMessage() {}

func (x *ProtServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtServiceInfo.ProtoReflect.Descriptor instead.
func (*ProtServiceInfo) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{0}
}

func (x *ProtServiceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProtServiceInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ProtServiceInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 服务信息获取请求
type GetServiceInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务名称，如果为空("")则获取所有服务信息
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoRequest) Reset() {
	*x = GetServiceInfoRequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoRequest) ProtoMessage() {}

func (x *GetServiceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{1}
}

func (x *GetServiceInfoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 服务信息获取响应
type GetServiceInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务信息列表
	Services      []*ProtServiceInfo `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoResponse) Reset() {
	*x = GetServiceInfoResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoResponse) ProtoMessage() {}

func (x *GetServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{2}
}

func (x *GetServiceInfoResponse) GetServices() []*ProtServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

// * 通讯协议数据域组织请求 Communication Protocol DataDomain Organization
// DL/T 698.45 协议为例：
//
//	{
//	    "type": 0x0100,      // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
//	    "addr": "",          // 地址
//	    "data": {
//	        ... // 根据请求类型不同，传递不同的参数
//	    },
//	    "timetag": 0        // 时间标签，0 无时间标签（默认无时间标签，可不传）
//	}
type CPDORequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务名称, 可用协议类型的 string 格式进行替代
	// 协议类型 -> 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 组织参数, JSON 格式
	Params        string `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDORequest) Reset() {
	*x = CPDORequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDORequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDORequest) ProtoMessage() {}

func (x *CPDORequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDORequest.ProtoReflect.Descriptor instead.
func (*CPDORequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{3}
}

func (x *CPDORequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CPDORequest) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

// 协议数据域组织响应
type CPDOResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织后的数据, 字符串格式
	Datadomain    string `protobuf:"bytes,1,opt,name=datadomain,proto3" json:"datadomain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDOResponse) Reset() {
	*x = CPDOResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDOResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDOResponse) ProtoMessage() {}

func (x *CPDOResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDOResponse.ProtoReflect.Descriptor instead.
func (*CPDOResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{4}
}

func (x *CPDOResponse) GetDatadomain() string {
	if x != nil {
		return x.Datadomain
	}
	return ""
}

// * 通讯协议组织请求 Communication Protocol Organization
// DL/T 698.45 协议为例：
// 1. 先组织的数据域（APDU）
//
//	"params": {
//	    "addr": ""
//	    "datadomain": ""
//	}
//
// 2. 传入不同数据直接组织协议报文
//
//	"params": {
//	    "type": 0x0100,  // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
//	    "addr": "",      // 地址
//	    "data": {
//	        ... // 根据请求类型不同，传递不同的参数
//	    },
//	    "timetag": 0    // 时间标签，0 无时间标签（默认无时间标签，可不传）
//	}
type CPORequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务名称, 可用协议类型的 string 格式进行替代
	// 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 组织参数, JSON 格式
	Params        string `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPORequest) Reset() {
	*x = CPORequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPORequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPORequest) ProtoMessage() {}

func (x *CPORequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPORequest.ProtoReflect.Descriptor instead.
func (*CPORequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{5}
}

func (x *CPORequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CPORequest) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

// 协议组织响应
type CPOResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织后的数据, 字符串格式
	Frame         string `protobuf:"bytes,1,opt,name=frame,proto3" json:"frame,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPOResponse) Reset() {
	*x = CPOResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPOResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPOResponse) ProtoMessage() {}

func (x *CPOResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPOResponse.ProtoReflect.Descriptor instead.
func (*CPOResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{6}
}

func (x *CPOResponse) GetFrame() string {
	if x != nil {
		return x.Frame
	}
	return ""
}

// 通讯协议解析请求 Communication Protocol Parsing
type CPPRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务名称, 可用协议类型的 string 格式进行替代
	// 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 待解析的数据, 字符串格式
	Frame         string `protobuf:"bytes,2,opt,name=frame,proto3" json:"frame,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPPRequest) Reset() {
	*x = CPPRequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPPRequest) ProtoMessage() {}

func (x *CPPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPPRequest.ProtoReflect.Descriptor instead.
func (*CPPRequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{7}
}

func (x *CPPRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CPPRequest) GetFrame() string {
	if x != nil {
		return x.Frame
	}
	return ""
}

// 协议解析响应
type CPPResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 解析后的数据, JSON 格式
	Data          string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPPResponse) Reset() {
	*x = CPPResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPPResponse) ProtoMessage() {}

func (x *CPPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPPResponse.ProtoReflect.Descriptor instead.
func (*CPPResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{8}
}

func (x *CPPResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// 通讯协议数据域解析请求
type CPDRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议服务名称, 可用协议类型的 string 格式进行替代
	// 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 待解析的数据, 字符串格式
	Datadomain    string `protobuf:"bytes,2,opt,name=datadomain,proto3" json:"datadomain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDRequest) Reset() {
	*x = CPDRequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDRequest) ProtoMessage() {}

func (x *CPDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDRequest.ProtoReflect.Descriptor instead.
func (*CPDRequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{9}
}

func (x *CPDRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CPDRequest) GetDatadomain() string {
	if x != nil {
		return x.Datadomain
	}
	return ""
}

// 协议数据域解析响应
type CPDResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 解析后的数据, JSON 格式
	Data          string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPDResponse) Reset() {
	*x = CPDResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPDResponse) ProtoMessage() {}

func (x *CPDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPDResponse.ProtoReflect.Descriptor instead.
func (*CPDResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{10}
}

func (x *CPDResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_api_protocol_protocol_proto protoreflect.FileDescriptor

const file_api_protocol_protocol_proto_rawDesc = "" +
	"\n" +
	"\x1bapi/protocol/protocol.proto\x12\bprotocol\"a\n" +
	"\x0fProtServiceInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"+\n" +
	"\x15GetServiceInfoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"O\n" +
	"\x16GetServiceInfoResponse\x125\n" +
	"\bservices\x18\x01 \x03(\v2\x19.protocol.ProtServiceInfoR\bservices\"9\n" +
	"\vCPDORequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06params\x18\x02 \x01(\tR\x06params\".\n" +
	"\fCPDOResponse\x12\x1e\n" +
	"\n" +
	"datadomain\x18\x01 \x01(\tR\n" +
	"datadomain\"8\n" +
	"\n" +
	"CPORequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06params\x18\x02 \x01(\tR\x06params\"#\n" +
	"\vCPOResponse\x12\x14\n" +
	"\x05frame\x18\x01 \x01(\tR\x05frame\"6\n" +
	"\n" +
	"CPPRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05frame\x18\x02 \x01(\tR\x05frame\"!\n" +
	"\vCPPResponse\x12\x12\n" +
	"\x04data\x18\x01 \x01(\tR\x04data\"@\n" +
	"\n" +
	"CPDRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"datadomain\x18\x02 \x01(\tR\n" +
	"datadomain\"!\n" +
	"\vCPDResponse\x12\x12\n" +
	"\x04data\x18\x01 \x01(\tR\x04data2\x8a\x03\n" +
	"\x13BaseProtocolService\x12S\n" +
	"\x0eGetServiceInfo\x12\x1f.protocol.GetServiceInfoRequest\x1a .protocol.GetServiceInfoResponse\x12O\n" +
	"\x1eProtocolDataDomainOrganization\x12\x15.protocol.CPDORequest\x1a\x16.protocol.CPDOResponse\x12C\n" +
	"\x14ProtocolOrganization\x12\x14.protocol.CPORequest\x1a\x15.protocol.CPOResponse\x12>\n" +
	"\x0fProtocolParsing\x12\x14.protocol.CPPRequest\x1a\x15.protocol.CPPResponse\x12H\n" +
	"\x19ProtocolDataDomainParsing\x12\x14.protocol.CPDRequest\x1a\x15.protocol.CPDResponseB&Z$tp.service/service/bean/api/protocolb\x06proto3"

var (
	file_api_protocol_protocol_proto_rawDescOnce sync.Once
	file_api_protocol_protocol_proto_rawDescData []byte
)

func file_api_protocol_protocol_proto_rawDescGZIP() []byte {
	file_api_protocol_protocol_proto_rawDescOnce.Do(func() {
		file_api_protocol_protocol_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_protocol_protocol_proto_rawDesc), len(file_api_protocol_protocol_proto_rawDesc)))
	})
	return file_api_protocol_protocol_proto_rawDescData
}

var file_api_protocol_protocol_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_protocol_protocol_proto_goTypes = []any{
	(*ProtServiceInfo)(nil),        // 0: protocol.ProtServiceInfo
	(*GetServiceInfoRequest)(nil),  // 1: protocol.GetServiceInfoRequest
	(*GetServiceInfoResponse)(nil), // 2: protocol.GetServiceInfoResponse
	(*CPDORequest)(nil),            // 3: protocol.CPDORequest
	(*CPDOResponse)(nil),           // 4: protocol.CPDOResponse
	(*CPORequest)(nil),             // 5: protocol.CPORequest
	(*CPOResponse)(nil),            // 6: protocol.CPOResponse
	(*CPPRequest)(nil),             // 7: protocol.CPPRequest
	(*CPPResponse)(nil),            // 8: protocol.CPPResponse
	(*CPDRequest)(nil),             // 9: protocol.CPDRequest
	(*CPDResponse)(nil),            // 10: protocol.CPDResponse
}
var file_api_protocol_protocol_proto_depIdxs = []int32{
	0,  // 0: protocol.GetServiceInfoResponse.services:type_name -> protocol.ProtServiceInfo
	1,  // 1: protocol.BaseProtocolService.GetServiceInfo:input_type -> protocol.GetServiceInfoRequest
	3,  // 2: protocol.BaseProtocolService.ProtocolDataDomainOrganization:input_type -> protocol.CPDORequest
	5,  // 3: protocol.BaseProtocolService.ProtocolOrganization:input_type -> protocol.CPORequest
	7,  // 4: protocol.BaseProtocolService.ProtocolParsing:input_type -> protocol.CPPRequest
	9,  // 5: protocol.BaseProtocolService.ProtocolDataDomainParsing:input_type -> protocol.CPDRequest
	2,  // 6: protocol.BaseProtocolService.GetServiceInfo:output_type -> protocol.GetServiceInfoResponse
	4,  // 7: protocol.BaseProtocolService.ProtocolDataDomainOrganization:output_type -> protocol.CPDOResponse
	6,  // 8: protocol.BaseProtocolService.ProtocolOrganization:output_type -> protocol.CPOResponse
	8,  // 9: protocol.BaseProtocolService.ProtocolParsing:output_type -> protocol.CPPResponse
	10, // 10: protocol.BaseProtocolService.ProtocolDataDomainParsing:output_type -> protocol.CPDResponse
	6,  // [6:11] is the sub-list for method output_type
	1,  // [1:6] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_api_protocol_protocol_proto_init() }
func file_api_protocol_protocol_proto_init() {
	if File_api_protocol_protocol_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_protocol_protocol_proto_rawDesc), len(file_api_protocol_protocol_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_protocol_protocol_proto_goTypes,
		DependencyIndexes: file_api_protocol_protocol_proto_depIdxs,
		MessageInfos:      file_api_protocol_protocol_proto_msgTypes,
	}.Build()
	File_api_protocol_protocol_proto = out.File
	file_api_protocol_protocol_proto_goTypes = nil
	file_api_protocol_protocol_proto_depIdxs = nil
}
