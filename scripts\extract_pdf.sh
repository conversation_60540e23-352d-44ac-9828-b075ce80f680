#!/bin/bash

# PDF 提取脚本
# 用于提取 DL/T 790.6-2010 标准文档内容

set -e

echo "=== PDF 内容提取工具 ==="
echo "提取 DL/T 790.6-2010 采用配电线载波的配电自动化 第6部分: A-XDR编码规则 标准"
echo ""

# 检查是否在项目根目录
if [ ! -f "go.mod" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查 PDF 文件是否存在
PDF_FILE="doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf"
if [ ! -f "$PDF_FILE" ]; then
    echo "错误: PDF 文件不存在: $PDF_FILE"
    exit 1
fi

echo "找到 PDF 文件: $PDF_FILE"
echo ""

# 选择提取方法
echo "请选择提取方法:"
echo "1) 基础提取 (使用 ledongthuc/pdf)"
echo "2) 高级提取 (使用 unidoc/unipdf - 推荐)"
echo ""
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo "使用基础提取方法..."
        go run cmd/pdf_extractor/main.go
        ;;
    2)
        echo "使用高级提取方法..."
        go run cmd/pdf_extractor_advanced/main.go
        ;;
    *)
        echo "无效选择，使用默认的高级提取方法..."
        go run cmd/pdf_extractor_advanced/main.go
        ;;
esac

echo ""
echo "=== 提取完成 ==="
echo "生成的文件位于: internal/protocol/dlt69845/datatype/"
echo ""
echo "您可以在 Go 代码中这样使用:"
echo ""
echo "import \"tp.service/internal/protocol/dlt69845/datatype\""
echo ""
echo "// 获取完整内容"
echo "content := datatype.DLT790_6.GetFullContent()"
echo ""
echo "// 查找特定章节"
echo "section := datatype.DLT790_6.FindSection(\"A-XDR\")"
echo ""
echo "// 获取所有章节"
echo "sections := datatype.DLT790_6.GetSections()"
