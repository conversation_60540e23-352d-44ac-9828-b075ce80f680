// Code generated by PDF extractor at 2025-06-28 16:46:45
// Source: doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf
// DO NOT EDIT manually

package datatype

// DLT790_6_Content contains the extracted content from DL/T 790.6-2010 standard
// 采用配电线载波的配电自动化 第6部分: A-XDR编码规则 标准
const DLT790_6_Content = `// ===== 第 1 页 =====

ICS 27.100F21备案号：29043-2010DL中华人民共和国电力行业标灌DL / T 790.6 — 2010 / IEC 61334 - 6: 2000采用配电线载波的配电自动化 第6部分：A-XDR编码规则Distribution automation using distribution line carrier system -Part 6: A-XDR encoding rule(IEC 61334-6: 2000, IDT)2010-05-24 发布2010-10-01 实施国家能源局发布

// ===== 第 2 页 =====

DL / T 790.6 — 2010 / IEC 61334 一 6: 2000目 次前言.......................................................................................n引言......................................................................................iv】范围...................................................................................  12规范性引用文件............................................................................13 A-XDR的一般特征............................................................................14编码结构..................................................................................25编码规则..................................................................................35.1 标识符域..........................................................................35.2 长度域..........................................................................  45.3 内容域.............................................................................  46编码过程................................................................................  46.1 整型（INTEGER）值的编码...............................................................46.2 布尔（BOOLEAN）型值的编码...............................................................66.3 枚举（ENUMERATED）型值的编码............................................................76.4 位串（BITSTRING）型值的编码.............................................................76.5 字节串（BYTE STRING）型值的编码...........................................................86.6 选择（CHOICE）型值的编码................................................................96.7 标记类型（隐式标记、显式标记和ASN.1显式标记）......................................96.8 可选（OPTIONAL）和默认（DEFAULT）数据项（COMPONET） ....................................................  116.9 序列（SEQUENCE）型值的编码..............................................................116.10 SEQUENCE OF 型值的编码.................................................................126.11 可视串（VisibleString）型的编码......................................................136.12 通用时间（GeneralizedTime）型的编码.......................................................146.13 ASN.1 空值（NULL）的编码................................................................14附录A （资料性附录）可扩展性...............................................................15附录R （资料性附录）DLMS中用的ASN.1类型和关键字..............................................16附录C （资料性附录）DLMS PDU的A-XDR编码示例...................................................17

// ===== 第 3 页 =====

DL/T 790.6 — 2010/IEC 61334 - 6:2000前口随着我国电网技术的发展，对配电自动化的要求已日益迫切。与传输配电自动化信息的其他通信方 式相比，配电线载波可以降低建设投资和运行费用，便于管理，是一种经济实用的通信方式。配电电压 不高，但电网结构复杂，信号传输衰减大。针对配电网信号传输特点，自1995年起，国际电工委员会 陆续发布了 1EC 61334系列的国际标准或技术报告。采用这些文件使之成为我国的标准文件对于我国这 方面工作的开展有很好的指导意义，便于与国际接轨。系列标准DL790《采用配电线载波的配电自动化》采用国际系列标准1EC61334《采用配电线载波 的配电自动化》，包括标准和标准化指导性技术文件，共有以下20部分。DL/Z79O.11采用配电线载波的配电自动化 第1部分：总则 第1篇：配电自动化系统的体系结构DL/Z 790.12采用配电线载波的配电自动化第1部分：总则第2篇：制定规范的导即DL/Z 790.14采用配电线载波的配电自动化 第1Y部分：总则 中低压配电线载波传输参数DL/T 790.31采用配电线载波的配电自动化 第3部分：配电线载波信号传输要求 第1篇：频带 和输出电平DUT79O.321采用配电线载波的配电自动化 第3.21部分：配电线载波信号传输要求 中压绝缘 电容型相相结合设备DL/T790.322采用配电线载波的配电自动化 第3.22部分：配电线载波信号传输要求 中压相地 和注入式屏蔽地结合设备DL/T 790.41采用配电线载波的配电自动化 第4部分：数据通信协议 第1篇：通信系统参考模 型DUT 790.432 路控制DI7T 790.433 接的协议DL" 790.441 报文规范DL/T 790.442采用配电线载波的配电自动化 采用配电线载波的配电自动化 采用配电线载波的配电自动化 采用配电线载波的配电自动化第4-32部分：数据通信协议 数据链路层一逻辑链第4.33部分：数据通信协议数据链路层面向连 第4Y1部分：数据通信协议应用层协议一配电线第442部分：数据通信协议应用协议应用层DLTT 790.4511采用配电线载波的配电自动化第4-511部分：数据通信协议系统管理C1ASE协议DLTT 790.4512采用配电线载波的配电自动化 第4-512部分：数据通信协议 系统管理 采用DL/T 790.51协议集的系统管理信息库DL/T 790.461DL/T 790.51 (S-FSK)协议DL/Z 790.52DL/Z 790.53 协议DL/Z 790.54采用配电线载波的配电自动化 采用配电线载波的配电自动化采用配电线载波的配电自动化 采用配电线载波的配电自动化采用配电线载波的配电自动化第4-61部分：数据通信协议网络层无连接协议第5部分：低层协议集第5.2部分：低层协议集第5-3部分：低层协议集第S4部分：低层协议集第1篇：扩频型移频键控移频键控（FSK）协议自适应宽带扩飘（SS-AW）多载波调制（MCM）协议DL/Z 790.55采用配电线载波的配电自动化第5・5部分：低层协议集快速跳频扩频通信II

// ===== 第 4 页 =====

DL / T 790.6 —2010/IEC 61334 - 6: 2000Integerl6 :尸 INTEGER (-32768〜32767)Unsigned 16 ::= INTEGER (0—65535)假设对A和B编码，其值分别为0x1234和0x5678 、上述序列的BER编码如下:308 | 02 | 0212 34C2 1 0256 78序列标识......序列长度......A的标识（整数＞-A的长度-------A的数值.......B的标识（用数）一B的长度-------B的数值.......同一序列的A.XDR编码如下：
「I | 12 34 | 56 78序列［SEQUENCE” ］标识-----------A的数值一一-—-—^一 ..............B的数值....................................5编码规则对任何ASN.1类型的数据进行A・XDR编码，其字节数都是整数，每个字节有8位。这些字节从最 外层的ASN.1类型的标识符域编玛的第一个字节开始，可认为这个字节为最高位。DL" 790的本部分 做以下规定：--不系统地对A-XDR编码的字节编号，但有时，为便于理解可加说明（例如，值的第1字节等）。--每个字节的位的编号为】〜8,其中第8位是最高位。5.1 标识符域标识符域的作用是确定编码值的类型。假如信息的发送端和接收端都以相同抽象句法规范运行，只 在以下情况下标识符域才传输信息：a）应从不同CHOICE类型中选择一种数据类型；b） 应指出序列（SEQUENCE）中有可选（OPTIONAL）项存在；c）应指出序列（SEQUENCE）中有默认（DEFAULT）项存在。A-XDR只在上述情况时有标识符域。此外，当ASN.1规范（ASN.1的显式标记，见6.7）要求对标 识符域进行编码时，A-XDR对标识符编码。在a）情况下，A・XDR要求选择（CHOICE）的所有备选在ASN.1中定义为显式标记类型。这时， 编码标识形成了标识符域。在b）和c）情况下，可选（OPTIONAL）和默认（DEFAULT）项是否存在可由布尔（BOOLEAN） 类型的存在标记表示。这些可选项值的标识符域就是存在标记值的A-XDR编码（见6.9）。另一方面，当ASN.1定义中包含ASN.】的显式标记（见6.7）时，A-XDR就必须对标识符域编码。 这些类型的A-XDR编码定义和它们的BER编码相同。这样做的目的是使对长度编码,便于省略一些结构。7） Ox…表示后面的数是十六进制的。8） A-XDR只在特殊情况下需要编码标识（例如：当本例的SEQUENCE是CHOICE类型的选择中的一个时）。 

// ===== 第 5 页 =====

DL/T 790.6 —2010/ IEC 61334 - 6: 2000这些类型的标识符域是ASN.l标记的编码值，所占字节数是整数，至少为L如ITU-T的X.209规定。5.2 长度域在A-XDR中长度域（如存在）位于内容域的前面。它明确地表示内容区的长度，所占字节数为整 数。如信息的发送端和接收端都以相同的抽象句法规范运行，只在对可变长度的ASN.1类型编码时长度 域才传输信息。可能的情况如下：a）长度可变的整数（INTEGER）；b）长度可变的位串（BIT STRING）；c）长度可变的字节串（BYTE STRING）：d）长度可变的类型序列（SEQUENCEOF）。只在以上情况以及ASN.l规范（ASN.1显式标记，见6.7）要求时，A-XDR才对长度域编码。在a）、b）、c）和d）四种情况下，长度域编码为一个可变长度的整数。其他情况，除只有确定形 式可以［见脚注7］使用的限制外，A-XDR采用与BER定义相同的方法（见ITU・T的X.209）。不允许 A-XDR的长度域用不确定型。5.3 内容域内容域是编码的本体。它传输实际值，由零或多个字节组成。以下条款规定了数值编码的方法。6编码过程6.1 整型（INTEGER）值的编码A-XDR为ASN.1整型提供了两种编码方式。具体用唧:外力式取决于整型的ASN.1定义的值是否受 约束。如一个整型的数值范围确定时（例如数值范围为-128〜127）,按固定长度的整数编码：如数值范 围未确定，按可变长度的整数编码。6.1.1 固定长度的整数值编码A-XDR为固定长度的整数提供了两种不同的编码。对于值不是负数的整数，可以将它表示并编码 为无符号二进制数：对于值可以为负数的整数，可以将它表示并编码为2的补码表示的二进制数。在这 两种情况下，只有整数的值被编码，并形成编码的内容域。这样做的目的是使编码的长度最小。6.1.1.1 固定长度的无符号整型值编码当一个整型的数值范围为非负数时，它的编码是一个无符号的二进制数。编码的字节数由规定的取 值范围决定，与表示规定范围内任一取值必需啊最少字节数相等。固定长度的无符号整数的范围总是边 界对齐的。例：INTEGER （0-255）的编码为1个字节；INTEGER （0-256）的编码为2个字节；INTEGER （237〜256）的编码为2个字节。一个固定长度的无符号整数以无符号二进制符号表示。其编码是一个等于该整数值的无符号二进制 数，由第1字节的第8至第1位，接着第2字节的第8至第1位，以下依次为其他各字节，直到编码的 最后字节组成。例：一个值为61478的整型的A-XDR编码如下：第1字节 第2字节8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1

// ===== 第 6 页 =====

DL/T 790.6 —2010/IEC 61334 — 6: 20006.1.1.2 固定长度的有符号整数值编码一个整型的数值范围内包含负数时，可按二进制的补码进行编码。编码的字节数由其取值范围决定, 与表示规定范围内任一取值必需的最少字节数相等。一个固定长度的整数的范围总是边界对齐的。例：INTEGER （-32768〜32767）的编码为2个字节；INTEGER （-14300〜8700）的编码为2个字节；INTEGER （-32768〜32768）的编码为3个字节。编码是一个等于该整数值的以2的补码表示的二进制数，由第1字节的第8至第1位，接着第2字 节的第8至第1位，以下依次为其他各字节，直到编码的最后字节组成。例：一个值为-45783的整型（-50000〜1）的A.XDR编码如下：
第1字节 第2字节 第3字节8 7654321 876543 2 1 876543211 1 1 1 1 1 11 0 1 0 0 1 1 0 1 0 0 1 0 1 0 0 16.1.2 可变长度的整数值编码如一个整型的取值范围未规定，其编码是可变长度的整型编码。可变长度整数的编码有两种形式， 取决于被编码的值。如一个未受约束的INTEGER值位于0〜127 （0W值V128）之间，值的编码只有一个字节。该字节 的第8位显然为零。例：数值为123 （=0x7B）的INTEGER值的A-XDR编码如下：如一个未约束的整型值的取值范围超出上述OW值＜128,其编码有两个域：先是固定长度域，即长 度域Length,它表示后面的可变长度域的长度（字节数），其次是可变长度域，即内容域，它传输编码 值，有整数个字节。长度区在一个字节里编码! 第8位设置为1,表示长度区是存在的。其他7位编码为一个固定长 度的无符号整数，其值表示内容区的字节数。编码的字节数取决于编码的值.等于表示该值的必需的最少字节她编码是一个等于该整数值的以2的补码表示的二进制数，由第.4字节的第8至第1位，接着第2字 节的第8至第1位，以下依次为其他各字节，直到编码的最后字节组成，与固定长度的有符号整数值的 编码相同（见6.L1.2）。可变长度的整数值的编码结构如图4所示。值的第，字节值的第〃字节值的第1字节） 长度区（・力） 内容区长度区存在图4可变长度的整数值的编码结构9）长度域的值是。〜127之间的任意值，最多可表示1016位的整型编码。

// ===== 第 7 页 =====

DL/T 790.6 — 2010 / IEC 61334 - 6: 2000可变长度的整数值的A-XDR编码示例：a) 0的编码结构如下：8 7 6 5 4 3 2 1 00000000 + •• •— 长度区不存在b) -1的编码结构如下：87654321 87 65 4 3211 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1I___________________I______________________长度区(E) 内容区长度区存在c) 128的编码结构如值的第1字节 值的第2字节8 7 6 5 4 3 2 1 87654321 8 7 6 5 4 3 2 10. 0000000 1 0 0 0 0 0 0 0长度区(=2) 内容区长度区存在d) T28的编码结构如F：值的第1字节 值的第2字节87654321 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 11 0 0 0 0 0 1 0 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0长度区(=2) 内容区长度区存在6.2 布尔(BOOLEAN)型值的编码布尔型只能取两种值：TURE或FALSE。布尔值的A.XDR编码只有内容域，由一个字节组成。如 编码值为FALSE,这个字节为零(所有位都是零)；如值为TRUE,这个字节可以是任意非零值，由发 送者选择。例：FALSE布尔值的A-XDR的编码如下：TRUE布尔值的A-XDR的有效编码如E6

// ===== 第 8 页 =====

DL / T 790.6—2010 / IEC 61334 - 6: 20006.3 枚举(ENUMERATED)型值的编码枚举型A-XDR编码的取值范围为0〜255, 一个枚举型的A-XDR编码及取值范围与0〜255的整型 相同，可作为一个固定长度、受约束的无符号型整数INTEGER (0-255)编码，由一个字节组成。6.4 位串(BIT STRING)型值的编码根据位串在ASN.1定义中是否规定了其大小，A-XDR提供了两种编码方法：位串的大小规定时， 为固定长度编码；位串的大小未规定时，为可变长度编码。在这两种情况中，位串型编码的字节的边界 都是对齐的，通过增加0值的追踪位实现。6.4.1 规定大小的位串值的固定长度编码如位串的大小在ASN.1中己经规定，其A-XDR编码只有内容域。内容域的字节数由规定的大小确 定，其值等于传输位串中位的个数必需的最少字节数。例：BIT STRING (SIZE (3))的编码为I个字节：BIT STRING (SIZE (8))的编码为1个字节；BIT STRING (SIZE (14))的编码为2个字节。位串中的位从第1位开始到追踪位结束。先排第I个字节的第8至第1位，接着第2个字节的第8 至第1位，以下依次为各字节的第8至第1位，直到需要的最后一个字节，都由第8位开始。除最后一个字节外，其他每个编码的字节都包括位串的8位。最后一个字节包括位串余下的位以及 值为零的追踪位。例：值为 01100111O1O1O 的 BIT STRING (SIZE (13))的 A-XDR 编码如 F：第1字节 第2字节8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1
01100111 0101000,0- - • 一 一一一工 *BIT STRING的位 追除位
6.4.2 未规定大小的位串值的可变长度编码如一个位串在ASN.1定义中没有规定其大小，其A-XDR编码有两个域：长度域和内容域。长度域表示的值等于位串编码值的位的个数。长度域本身的编码规则和可变长度的整数的编码相 似。但因负值对于长度域没有意义，因此整数的编码为二进制编码.而不是2的补码表示的二进制数。 (与可变长度的无符号整数的编码相似)内容域传输位串的编码值有整数个字节。该域的编码规则与6.4.1相同。例：一个值为 0110011101010 的 BIT STRING 的 A-XDR 编码如下：第1字节 第2字节 第3字节87 6543 21 8 765432】 876543210 0 0 0 1 10 2 0)100111 । 010 10000Mt— -」—长度域(73) 位审的位只有长度区的值以I个字节编码 追踪位例：一个由131位组成的BIT STRING值的A-XDR编码如下：7

// ===== 第 9 页 =====

DL / T 790.6 —2010/IEC 61334 - 6: 2000第1字节 第2字节 第3字节 第i字节 第"字节87654321 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 11 0 0 0 0 0 0 11 0 0 0 0 0 1 1XXXXXXXX• ••XXX000001—1
]长度区箱码 的长度(=1)长度区(T31)的编码BIT STRING的位 追踪位BIT STRING的长度域 BIT STRING的内容域长度域信息以多个字节编玛6.5 字节率(BYTE STRING)型值的编码根据在ASN.1定义中是否规定了字节串的大小，A-XDR提供了两种编码方法。ASN.1定义中规定 了字节串的大小时，为固定长度编码；未规定字节串的大小时，为可变长度编码。6.5.1 规定大小的字节串的固定长度编码如字节串的大小在ASN.1定义中已经规定，则该字节串的A-XDR编码只有内容域。内容域中的字 节数等于规定的大小。字节串的字节从第一个起至最后一个，只需挂在内容域的各字节中。例：一个值为 “ABCD” 的 BYTE STRING (SIZE (4))的 A・XDR 编码如下：第I字节 第2字节 第3字节 第4字节8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 87654321 8 7 6 5 4 3 2 1BYTE STRING的字节
6.5.2 未规定大小的字节串的可变长度编码如在ASN.1定义中没有规定一个字节串的大小，其A.XDR编码包括两个域：长度域和内容域。长度域表示的值等于内容域的字节数。长度域本身的编码规则与可变长度的字节串的编码相同(见6.4.2 )o内容域传输字节串的编码值。其编码规则和651中规定的字节串相同。例：一个值为"ABC"的BYTE STRING的编码如下：第1字节 第2字节 第3字节 第4字节87654321 8 7 6 5 4 3 2 1 87654321 8 7 6 5 4 3 2 10 0 0 0 0 0 1 I 01000001 10100001 0 0 1 0 0 0 0 1 1二 土 二，上一I长度域(-3) 内容或=BYTE STRING的字节只有长度域的值以1个字节编码例：一个BYTE STRING的A-XDR编码由347个字节组成10)编码第"字节中的〃=长度域字节数+内容域字节数= 3 + 347 = 350。8

// ===== 第 10 页 =====

DL / T 790.6 — 2010 / IEC 61334 - 6: 2000第1字节 第2字节 第3字节 第4字节 第i字节 第八字节8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 87654321 8 ••• 1 8 7 6 5 4 3 2 110 0 0 0 0 100 0 0 0 0 0 0 10 10 110 11XXXXXXXXX ... XXXXXXXXX长度城编码 长度域(=347)的编码=OxO15B BYTE STRING的字节的长度(T)BYTE STRING的长度城 BYTE STRING的内容域长度城信息以多个字节维码6.6 选择(CHOICE)型值的编码A-XDR的一个主要概念是信息的发送端和接收端按相同的抽象句法运行时，BER编码的标识符域 在大多情况下传输的是冗余的信息。如不而标识符编码不会导致编码含糊不清。因此,A-XDR不对ASN.1 型的标识区进行系统的编码。选择ASN.1类型以各数据项类型的集合定义，它的可选择类型必须十分明确。每一个选择类型的值 是它的一个可选项"〉，编码规则应保证任一可选项的编码值不会被含糊地标识。因此，选择类型的值的 编码应有标识符域。为了表达清楚，一个选择类型的ASN.1的所有数据项的类型都应是显式标记⑵。没有显式标记数 据项的选择类型不能用A-XDR编码。一个选择类型值的A-XDR编码应是选择的可选类型的£XDR编码，由标识符(标记)的一个字 节的编码开始，像固定长度的无符号整数一样。以下面的ASN.1选择类型的结构为例，Dummy_PDU::= CHOICE {a [0] INTEGER,b [1] BYTE STRING(SIZE(4))}当"a"被选择且a = 3715时，其A-XDR编码如下：第1字节 第2字节 第3字节 第4字节标识符域 I长度城(・2) 内容域
 长度区存在 •' 编码标志_________________________可变长度的INTEGER编码(3715 = 0x383)当“b"被选择且6= "ABCD"时，其A-XDR的编码如下:8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1第I字节 第2字节 第3字节 第4字节 第5字节0 0 0 0 0 0 0 I0 10 0 0 0 0 10 i 0 0 0 0 I 00 ! 0 0 0 0 I【0 1 0 0 0 I G 01___________________
(______标识域 _______j____________________________________________________________________________」编码标志 固定长度的BYTT STRING编码6.7 标记类型(隐式标记、显式标记和ASN.1显式标记)回顾一下6.6中的Dummy.PDU (虚拟协议数据单元):11) ASN.1选择类型与C语言的联合(UNION)类型类似。12)术语"标记"在6.7中定义。9

// ===== 第 11 页 =====

DL / T 790.6 — 2010/IEC 61334 一 6: 2000Dummy_PDU::= CHOICE {a [0J INTEGER,b [1] BYTE STRING（SIZE（4）） }符号[0]整型和口]字节串是ASN.l的标记的符号（由在原来类型的符号前加带方括号的数字得 来）。标记用以表示类型之间的区别。事实上，每种ASN.1类型（甚至像整型、位串类型等这些内部ASN.1型）都有其标记⑶。标记分 为四类，任何一类中的每种标记以正整数编号。这四类是：通用类、上下文相关类、应用范围类和专用 类。内部ASN.1型在通用类中有标记，称为基本型。通用类以外的所有类型的标记构成各种标0型。每种标记不是隐式的就是显式的，在标记型中可通过在“厂和基本型之间加关键字"IMPLICIT” 或"EXPUCIT”区别。如什么关键字也没有，则为缺省标记。应注意，关键字"IMPLICIT"和"EXPLICIT” 形容的是标记而不是标记型，并只在编码中起作用。在BER中，标记类型及其编号在标识符域传递。当其标记为隐式时，其值的BER编码有两个标记: 一个是新的，在方括号内规定:一个是原来的，是基本型的。编码常由嵌套编码，即由基本型的编码构 成。当标记为IMPLICIT时，标记值的BER编码只有新的标记，它代替原来的标记（包括类和编号）。例：——INTEGER 型的值-19374 （0xB452）的 BER 编码如 F：
02 02 B4 52标识符域-- -------- 」 ----内容域=0xB452INTEGER的标志类=通用 ---------字长域=2编码形式=原始 整数的标志号，2数值相同，但标记为[8]1NTERGER （显式标记，这是BER中的默认标记）的BER编码如下:标识符域...标志类=上下文相关编码形式=构造标志号=8标志型的值的内容域的长度域=4.........标志值的内容区一INTEGER的内容域INTEGER的长度域INTEGER的标识符域数值相同，但标记为^IMPLICIT INTEGER （IMPLICIT标记）的BER编码如下:
88 02 B4 52标识区------------------ ।.........内容域标志类=上下文相关 长度域编码形式=原始标志号=813）除选择类型的每种可选项有标记外，其他任何类型都有各种可能的标记。10

// ===== 第 12 页 =====

DL/T 790.6 —2010/ 1EC 61334 - 6: 2000在A.XDR中，显式标记和隐式标记的差别很小。如A-XDR完全不对非标记型的标识符域编码， 新的标记替代了旧的就没有意义。因此，隐式标记在BERH的意义并不适用于A・XDR。此外，有两种 显式标记适用于A-XDR,即A-XDR显式标记和ASN.1显式标记。在A-XDR中，为区分ASN.1的选择类型和序列（SEQUENCE）类型，当标记号明确地存在于ASN.1 规范中时，称为显式标记。这些被标记的数据项的编码规则：选择类型的数据项的标记在A-XDR中编 码为可变长度的整数，表示标记的值；序列类型的数据项的标记完全不编码。如不论"IMPLICIT”关 键字是否存在于ASN.1规范中，这些显式标记型的A-XDR编码都是相同的，因此可以认为在A-XDR 中可以忽略"IMPLICIT"关键字。当标记的类型和编号在ASN」规范中己经明确规定时，例如[APPLICATION 30],其类型符号是 指A-XDR中的ASN.1的显式标记.这种标记类型只适用于序列类型的数据项.除长度域不允许用不确定类型外，ASN.1显示标记类型的值的A-XDR编码和它的BER编码相同。 这种标记的目的只是使长度必须编码，从而便于在结构上有所省略。6.8 可选（OPTIONAL）和默认（DEFAULT〉数据项（COMPONET）任一种ASN.1复合类型都含有带ASN.1关键字"OPTIONAL"和"DEFAULT"的数据项。在ASN.1 规范中，这些关键字可以放在数据项类型的后面，用以十分直观地表达它的意思。带有可选“OPTIONAL" 标记的数据项可能被省略，它的值在编码中不一定存在（省略这些数据项的实际条件和意义应由设计者 规定）。一个数据项是可选的原因之一是假设它为一个特定值时（一般来说，该值经常出现）可以将它省略。 用这种方法可以避免显式地传送这个值。ASN.1的关键字"DEFAULT"用来表示数据项的默认值。一般来说，一个数据项是可选值还是默认值很不一样（在ASN.1中它们相互排斥）。可选数据项可 以完全省去，而DEFAULT数据项实际上总是存在的，因为即使将它省去它也还代表-•个特定值。可选或默认数据项的A.XDR编码由使用标记（usage flag）的附加元素（这是ASN.1语法的补充，ASN.1语法没有规定此元素）开始。这个使用标记为布尔型，其值表示可选或默认数据项的值是否在编码中存在，如下所示：—OPTIONAL 数据项:: usage flag = TRUE usage flag = FALSE-DEFAULT数据项：该数据项存在于•编码中 该数据项不存在于编码中usage flag = TRUE该数据项存在于编码中（其值和DEFAULT值不同）usage flag = FALSE 该数据项不存在于编码中（传送的是DEFAULT值）使用标记表明所述的数据项存在于编码中时•使用标记位于可选或默认数据项值的A・XDR编码之 后。如使用标记表明该数据项不存在于编码中，该数据项以使用标记的编码结束A-XDR编码，后面不 再有数据项的值的编码。在6.9和附录C中给出了可选和默认数据项的编码示例。6.9 序歹I] （SEQUENCE）型值的编码与选择类型类似，ASN.1序列类型也是根据数据项类型的集合定义的，他们都非常明确。但是与选 择类型不同的是，序列类型中的每一数值都有每种数据项类型的一个值⑷。数据项值在编码中出现顺序 是固定的，与定义中各数据项类型出现的次序相同。序列值的A-XDR编码应是从序列型的ASN.1定义表所列每一类型中的数据值的A-XDR编码，按 它们在定义中的顺序出现，除非该类型与关键字可选“OPTIONAL"或默认"DEFAULT”有关。序列值的有显式标记数据项的标记是冗余的信息，因此不对该标记编码，即有显式标记的数据项值14）数据项类型标为OPTIONAL或DEFAULT的除外（见6.7和6.8）。 11

// ===== 第 13 页 =====

DL / T 790.6 — 2010 /IEC 61334 - 6:2000的A-XDR编码是该数据项值的A-XDR编码。如可选或默认数据值的编码存在，它在编码(按6.8的规定)中出现的位置与在ASN.1定义中出现 的相对应。以下面的ASN.1定义为例：Dummy_PDU SEQUENCE {a INTEGERS... 127),b OCTET STRING(SIZE(4)) OPTIONAL,c [1] BOOLEAN DEFAULT TRUE,}——当 a = 37, b= “ABCD”，c = FALSE 时，该 SEQUENCE 型值的 A・XDR 编码如 F：第1字节 第2字节 第3第4第5第6 第7字节 第8字节8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 10 0 1 0 0 1 0 10 0 0 0 0 0 0 10x410x420x430x440 0 0 0 0 0 0 100000000
ja的编码 附使用标记为TRUE 固定长度BYTE c的使用标记为TRUE c的编码(37=0x25) 衰示OPTIONAL存在 STRING的Jfi码 表示DEFAULT存在'(FALSE)SEQUENCE第一元素的值 SEQUENCE第二元素的值 SEQUENCE第三元素的值——当a = 37, b不存在，c = FALSE时，该SEQUENCE型值的A-XDR编码如下:第俘节 第2字节 第3字节 第4字节87654321 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 10 0 1 0 0 10 10 0 0 0 0 0 0 !0 0 0 0 0 0 0 100000000a的编码 b的使用标记为FALSE c的使用标记为TRUE c的蜡码(37F25) 表示OPTIONAL 不存在 衰示 DEFAULT 存在 (FALSE)——当 a = 37, b= “ABCD”，c = TRUE （传送DEFAULT值〉时，该 SEQUENCE 型值的 A-XDR 编码如下：第1字节 第2字节 第3第4第5第6 第7字节0 0 1 0 0 1 0 10 0 0 0 0 0 0 10x410x420x430x440 0 0 0 0 0 0 1
i 」一 ____________a的编码 b的使用标记为TRUE 网定长度BYTE c的使用标记为FALSE(37-0x25) 表示OPTIONAL^在 STRING的编码' 表示DEFAULT不存在6.10 SEQUENCE OF型值的编码ASN.]的SEQUENCE OF型按单个数据项类型定义，它的值是数据项类型值的有序集合。根据SEQUENCE OF型在ASN.1定义中是否规定了其大小，A-XDR可提供两种编码方式。如在 ASN.1定义中已规定SEQUENCE OF的大小，用固定长度编码；如没有规定大小则用可变长度编码。6.10.1 规定大小的SEQUENCE OF型值的固定长度编码如在ASN.1中己规定SEQUENCE OF的大小，其A-XDR编码只有内容区。内容区的字节由ASN.1 定义所列类型的N个数据值的A-XDR编码组成，这里的N即为规定的大小。这些数据值的编码顺序和 被编码的SEQUENCE OF值的数据值相同。以下面的ASN.1类型定义为例：Dummy_List :尸 SEQUENCE (SIZE(2)) OF BIT STRINGBYTE STRING数据项的编码如下：12

// ===== 第 14 页 =====

DL / T 7go. 6 ——2010 /IEC 61334 - 6: 2000第 1 BYTE STRING： 00101第 2 BYTE STRING： i 1010010 1000 该SEQUENCE OF型的值的A-XDR编码如下:第1字节 第2字节 第3字节 第4字节 第5字节87654321 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 1第1 BIT STRING 第1BIT 追踪位 第2 BIT STRING 第2 BIT STRING的位 追踪位的长度(=5) STRING的位 的长度(=12)只有长度值以1个字节编码产有K度值以1个字节编码第1 BIT STRING值的A-XDR编码第2 BIT STRN5宜的A-XDR编叫6.10.2 未规定大小的SEQUENCE OF型值的可变长度编码ASN.1定义中未规定SEQUENCE OF的大小时，其A.XDR编码有两个区：长度域和内容域。长度区表示的值为SEQUENCE OF型的编码值的数据项的个数。K度区的编码方式和瓦变长度的 BIT STRING的长度区相同(见642)。内容区应由ASN.1的定义所列类型的N个数据值的A-XDR编码组成。这里的N为长度区表示的值。 这些数据值的编码顺序应与被编码的SEQUECE OF的值的顺序相同。以下面的ASN.1类型定义为例：Dummy_List ::= SEQUENCE OF INTEGER(0...4000)要编码的两个数据项为：INTEGER 1 = 1956INTEGER 2 = 3624该SEQUENCE OF型的上述值的A-XDR编码如下：第1字节 第2字节 第3字节 第4字节 第5字节87654 3 2 187654321 876543218765432 1876 5 43210 0 0 0 0 0 ) 0 00000JJ1 J 0 1 0 0 ) 0 0 0 0 0 0 1 1 1 0 0 0 1 0 1 0 0 0长度(=2纽件) 第I组件的编码(固定长度， 第2组件的编码(冏定长度，无符号整数，1956 = 07A4) 无符号整敷,3624 - OxOE28)6.10.3 特殊选择序列(SEQUENCE OF CHOICE)型的编码选择序列(SEQUENCE OF CHOICE)型是特殊情况。虽然SEQUENCE OF类型是按单个数据项类 型定义的，但选择序列结构的元素的类型不同，这就是选择类型的各种可选项。除SEQUENCE OF型的每个数据项的编码以所选的选择数据项的标记的编码开始外,选择序列的编 码方法与按6.10.1和6.10.2所述方法相同。附录C的［例C.5］为选择序列类型编码示例。6.11 可视串(VhibleString)型的编码可视串(VisibleString) ASN.1是一种限定的字符串类型。限定的字符串是一种特殊的字节串，可以 包含取自限定的字符集中的字符。虽然ASN.1规定了 8种类型，但只有可视串类型在DLMS规范中使 用(见DL/T 790.441—2004)。因此A-XDR只支持这种ASN.I类型ASN.1为每种特殊字符串类型都定义了通用类的标记。像其他ASN.1的内部类型的标记一样，在 A-XDR中这些标记不编码.因此，可视串的编码方法与字节串相同。如DL" 790.441-2004中只规定15)虽然其他ASN.1限定的字符串类型也可能用这种编码方法。13

// ===== 第 15 页 =====

DL/T790.6 —2010/IEC61334 -6: 2000可变长度的可视串，则只可以使用可变长度的字节串(见652)。 以可视串的"IEC”值的A-XDR编码为例：第1字节 第2字节 第3字节 第4字节8 7 6 5 4 3 2 1 8 7 6 5 4 3 2 ! 8 7 6 5 4 3 2 1 87654321长度(=3) 内容域』VisibkString的字节只有长度值以I个字节编玛6.12 通用时间(GeneralkedTime)型的编码通用时问(GcneralizedTime)以可视串(VisibleString)为基础，是一种ASN.1的有用型(UscfU】 Type), 定义如下：GeneralizedTime ::= [UNIVERSAL 24] IMPLICIT X^sibleString如A-XDR不对通用(UNrVERSAL)类ASM 1的标记编码，则与可视串编码相似，通用时间型的 A-XDR编码和字节串相同。也和可视串的原因一样，可变长度的字节串的编码适用于通用时间型的编 码(见652)。6.13 ASN.1空值(NULL)的编码ASN.1的空值(NULL)是一种很特殊的类型：只有一个值。因此，容纳信息的能力很有限。人们 可能认为空值没有用，然而，在上下文内容很少的情况下，例如，必须送出一个数据类型，但又不需要 传送任何信息时，它还是很有用的。可以认为空值丰富了一些没有值的类型的域。例如，要发送一个BOOLEAN值，这个值有时已知，有时又不存在，就可用以下类型： OutputValuc :尸 CHOICE {Known [0] BOOLEANUnknown [1]NULL}为了能用A-XDR编码，空值应是标记型。用6.7规定的标记值A-XDR编码的方法进行空值的A-XDR编码。14

// ===== 第 16 页 =====

DL / T 790.6 — 2010/1EC 61334 - 6: 2000附录A （资料性附录） 可扩展性第3章曾提到A-XDR编码规则与BER不同，是不可扩展的。这是什么含义呢？ 下面以SEQUENCES为例：dummy _sequencc 1 SEQUENCE （ a INTEGER,b INTEGER}dummy sequence_2 SEQUENCEa INTEGER, c BOOLEAN OPTIONAL, b INTEGER }SEQUENCE值的BER编码在OPTIONAL数据项“c"不存在相同。这意味着，如采用BER,在应 用协议将来的版本中，可以将更多的可选数据项加到序列中。如这些数据项被省略，例如，用一个按老 规范开发的过程进行通信时，这个过程表现相同。BER编码规则的这一性质称为可扩展性。从这种意义上说，A・XDR编码是不可扩展的。实际上，由以上讨论可知，即使"c”不存在，A-XDR 编码还与可选（OPTIONAL）数据项有关。另一方面，A.XDR也不是封闭的。它不但可以为现行DLMS规范中的ASN.1类型编码，如必要， 还可以为其他ASN.1类型编码，例如实数（REAL）、集合（SETOF）等。注：IECTC57的第9工作组负责将A-XDR不断更新。15

// ===== 第 17 页 =====

DL/T790.6 — 2010/IEC61334 - 6: 2000附录B（资料性附录）DLMS中用的ASN.1类型和关键字以下内部ASN.1类型用于DLMS规范（见DL/1790.441—2004）：INTEGERBOOLEAN ENUMERATEDBYTE STRINGBIT STRINGVisibleStringCHOICESEQUENCESEQUENCE OF NULL（整数）（布尔）（枚举）（字节串）（位串）（可视串）（选择）（序列）（空序列）DL/T 790.441—2004 也用一种 ASN.1 Useful Type （有用型），即GeneralizedTime（通用时间）在DLMS中定义了以下一些有用类型:Integer8Integer! 6Integer32Unsignod8Unsigned 16Unsigned32（8位整数）（16位整数）（32位整数）（8位无符号）（16位无符号）（32位无符号）在DLMS规范中还用了以下一些ASN.1关键字:IMPLICITOPTIONALDEFAULT（显式） （可选） （默认）16

// ===== 第 18 页 =====

DL / T 790.6 —2010/IEC 61334 - 6:2000附录C（资料性附录）DLMS PDU的A-XDR编码示例DLMSPDU的最外层的ASN.I类型是选择（CHOICE）类型。这里只提出一些未加密的，按以下定 义的协议数据单元作为示例：DLMSpdu ::= CHOICE {confirmScrviceRcquest [0]ConfinnedServiceRequest,InitiateRequestrnIMPLICITInitiateRequest,getStatusRequest[2]IMPLICITGetStotusRequest,getNameListRequestPlIMPLICITGetNameListRequest,gctVariableAttributeRequest[4]IMPLICITGctVariableAttributeRequestreadRequest[51IMPLICITReadRequestwriteRequest⑹IMPLIOTWriteRequestconfirmedServiceResponseP]ConfirmedServiceResponse,initiateResponse网IMPLICITInitiateResponsegetStatusResponse[9]IMPLICITGetStatusResponse,gctNamcListResponse[10]IMPLICITOctNameListResponsegetVhriableAttributcResponse[11]GetVhriableAttributeRespoosereadResponse【12]IMPLICITReadResponsewriteResponse[13]IMPLIOTWriteResponseconfinnedServiceRequest[14]ConfinnedServiceRequestunconfinnedSeTviceRequest[15]UnconfinnedServiceRequestabortRequest[16]IMPLICITAbortRequestonsolicitedWriteRequest[17]IMPLICITUnsolicitedWriteRequestunsolicitedServiceRequest[18]UnsolicitedServiceRequestinfbrmationReportRequest【】9]IMPLICITInfbrmationReportRequest相应于加密的协议数据单元的其他选择:ded-infbrmationReportRequest [88] IMPLICIT BYTE STRING }[例 C.1] InitiateRequest DLMS PDU 的 A-XDR 编码。InitiateRequest型的定义如下：InitiateRequest： —SEQUENCE { dedicated-key respond-allowed proposed-quality-cf-service proposed-dlms-version-number proposed-confbrmance proposed-max-pdu-sizeBYTE STRING OPTIONAL, BOOLEAN DEFAULT TRUE, ⑼ IMPLICIT Integer OPTIONAL, Unsigned8,Comfbnnance,Unsignedl6其中，Conformance可能如下:17

// ===== 第 19 页 =====

DL / T 790.6 — 2010/1EC 61334 - 6, 2000Conibrmance::=[APPLICATION 30] IMPLICIT BIT STRING (SIZE(16))get-data-set-attributegel-ti-attribute⑴,get-variable-attribute⑵，read(3),write(4),unconfirmedWrite(5),change-scope(6),stop-resume(7),make-usable(8),data-set-load⑼，selection-in-get-name-list(ID.detailed-access-low-bit(12),detailed-access-high-bit(】3),multiple-variable-list(14),data-set-upload(15)}本例优先采用以下值：dedicated-keyn不存在response-allowed=TRUE （默认值）prcposed-quahty-of-service=存在，值为4prcposed-dlms-version-number=1prcposed-max-pdu-size-134( = 0x86)prc-posed-confbrmance= 0xlC00,如下所示:具有上述值的initiateRequest PDU的A-XDR编码如下:B0BlB2B3B4B5B6B7B8B9B10BllB12B13
叫B150001110000000000-OxlCOO16101 DLMS PDU CHOICE （InitiateRequest）的（显式）标记00 dedicate&key数据项的使用标记（FALSE,不存在）0Q rcspo口sc・alk）wcd数据项的使用标记（FALSE,传送DEFAULT值〉01 proposedpuality-of-service 数据项的使用标记（TRUE,存在）04015E0300103Cproposed-quality-of-service 数据项的编码（4） proposcd-dlms-version-numbcr 数据项的编码（1〉[APPLICATION 30]标记（ASN.I显式标记）的编码T内容区字节长度（3）编码最后一个字节中未用的位的编号（0）Comformance数据项的BER编码Proposed-conformancd 数据项的编码0086proposed-max-pdu-siye 数据项值的编码(0x86)16） OxiCOO值表示除支持必备服务外建议也支持以下服务：读（第3位）、写（第4位）、非确认写（第5位）。18

// ===== 第 20 页 =====

DL / T 790.6 — 2010 / IEC 61334 - 6: 2000[例 C.2] InitiateResponse DLMS PDU 的 A-XDR 编码。InitiateResponsc 型定义如 F：InitiateResponse： ： = SEQUENCEnegotiated-quality-of-service negotiated-dlms-version-number negotiated-confbrmance negotiated-max-pdu-sizevaa-nane[0J IMPLICIT Integers OPTIONAL,Unsigned8, Conformance.Unsigned 16, Obj ectNameConfbrmance的定义与［例C.l］相同，ObjectName定义如下：ObjectName ： ： = Integer 16此外，vaa-name是有效的，这意味着vaa-name为111 （即vaa-name Integerl6的最低三位为111 ）。 Negotiated-quality-of^servicex negotiated-dlms-versioIl>numbe^^ negotiated-confbrmance 和 negotiated-max-pdu-size的值与［例C.1］相同；vaa-name的值选择为0x0037。 InitiateRequesl PDU为上述值时，A-XDR编码如下：08 DLMS PDU CHOICE （InitisteResponse）的（显式）标记01 negotiated-qualiiyHf-scrvke 数据项的使用标记（TRUE,存在）04 negotiated-qualityef-service 数据项的编码（4）01 negotiatedpuaHty>o&service 的编码（1）5E ［APPLICATION 30］标记（ASN.1豆式标记）的编码一03 内容区字节长度（3） 一致性数据项的BER编码00 最后一个字节中未使用位的号（0）1C00 negotiatedYonformance 数据项的编码 一00 -■ 86 negotiated-max-pdu^size 数据项的编码（0x86）00 37 vaa-name 值的编码（0x0037）［例C.3］确认的服务出错PDU的A-XDR编码。本例说明当InitiateRequest服务不被DLMS服务器接受时的DLMS PDU的A-XDR编码。服务不被 接受的原因是建议的内容不适用于所用服务器的服务表。Confirmed Service Error （确认的服务出错）定义如下：ConfirmedScrviccError : {-标记0保留 initiateError getStatus getNameList=CHOICE[1] ServiccError[2] ServiceError[3] ServiceErrorterminateUpload[19] ServiccError其中ServiceError意义如下:19

// ===== 第 21 页 =====

DL / T 790.6 — 2010/ IEC 61334 - 6: 2000ServiceError ： ： = CHOICE{• ••Initiate[6] IMPLICIT ENUMERATED-初始化服务出错otherdlm-version-too-low incompatible-confbnnance pdu-size-too-short refused-by-the-VDE-Handler(0),(1), 一建议的DLMS版本太低(2), -建议的服务不够用(3), 一建议的PDU尺寸太大(4)-不能或不允许创建vaa因此，在上述情况下，ConfirmedServiceError PDU的A-XDR编码如下：0E DLMS PDU CHOICE 的(显示)标记(ConfirmedServiceError = 14)01 ConfirmedServiceError CHOICE 的(显式).标记(jnitiateEiror = 1)06 ServiceError CHOICE 的(显式)标记(initiate - 6)02 ENUMERATED 值的编码(incompatiblxjonfofmancenZ)[例C4]读状态请求/响应PDU的A.XDR编码-•本例说明在以下情况下读状态请求(Get Statue Itequest)PDU及其响应的A.XDR编码:——请求不需要条件参数(Indentify = FALSE)#--用于接收请求的VDE实例规定如下：Dummy_VDE： ： = VDEGetStatusRequest 定义如下：VDE-handler ,VDE-type 0x0001,Serial-Number “1234”，Vendor-Name ,Model ,Version-Number ,Resource ,List-oAvaa (7,15,23),Status READY)-VDE-handler在其他地方定义-VDE类型值(在其他地方定义) 一寸其他地方定义- 由制造商定义- 由制造商定义- 由制造商定义- 在其他地方定义.-本VDE中的VAA列表GetStatusRequest: :=Identify其中 Identify ::= BOOLEAN因此，在上述情况下，GetStatusRequest PDU的A.XDR编码如R02 DLMS PDU CHOICE 的(显式)标识(GetStatusRequest = 2)00 Identify BOOLEAN的A-XDR编码(FALSE表示响应中只有必备参数)GetStatusResponse 定义如下：GetStatjsResponse ::= SEQUENCE20

// ===== 第 22 页 =====

DL / T 790.6 —2010/IEC 61334 - 6: 2000vde-typcserial-nmuberstatus{readynocbangeinoperable} DEFAULT ready, list-of-vaaidentify{resourcevendor-namemodelversion-number} OPTIONALInteger 16,BYTE STRING, ENUMERATED(0),⑴，(2)SEQUENCE OF OhjectName, SEQUENCEVisibleStringVisibieStringVisibleStringUnsigned8相应丁标识为 FALSE 的 GetStatusRequest 的 GetStatusResponse PDU 的 A-XDR 编码如下:09 DLMS PDU CHOICE (GetStatusResponse)的(显式)标记00 一01 VDE-Type 值(Integcrl6 = 0x0001)的编码04 Serial-Number (可变长度)BYTE STRING 的长度(=4)31 一323334 Serial-Number BYTE STRING (=“1234”)的内容区00 状态数据项的使用标记.它不存在于编码中,7,03 List-of-vaa 表的长度(可变长度)SEQUENCE OF (=3)0007 表中第】个 VAA-Name (0x0007)00OF衰中第 2 个 VAA-Name (15 = OxOOOF)0017 表中第 3 个 VAA-Name (23 = 0x0017)00 OPTIONAL Identify SEQUENCE 的使月标记(FALSE,表示不存在) ［例C5］变量访问服务DLMS命名变量(Named Variable)定义如下：Dummy NamedVariable ::= NamedVariable17）此值为传送协议数据单元的默认值。21

// ===== 第 23 页 =====

DL/T 790.6 — 2010/IEC 61334 - 6: 2000Variable-Name0x0010,Scope-Of-AccessVDE特定，Scope-May-ChangeFALSE,LifetimeVDE,Type-DescriptionDummy_Type,Read-Write FlagREAD-WRITEAvailableTRUEDummy_Type ::= SEQUENCE {Number_Of_Counters Unsigned8,Coimter.Vahies SEQUENCE OF Unsignedl6} •作为ReadRequest和ReadResponse的例子，参照Dummy_NamedVariable,可以假设在VDE中该变 量的值如下： -Number_Of_Counters = 2Counters SEQUENCE OF 有以 F两个值：Counter_Value_l =318 (=OxOBE)Counter_Value_2 = 715 (RxO2CB)下面的示例耍用命名变量。 [例C5.1]读没有具体访问的命名变量。 ReadRequest Service 定义如下：ReadRequest ::= SEQUENCE OF VariableAccessSpecification其中：VariableAccessSpecification ::= CHOICE { variable-name [2] IMPLICIT ObjectName,detailed-access [3] IMPLICIT SEQUENCE{Variable-name ObjectNamcDetailed-access DetailedAcccss} } 这时,应注意没有具体访问(detailed access)的访问。要求Dummy NamedVariable的值的ReadRequest PDU的A.XDR编码如下：05 DLMS PDU CHOICE 的(显式)标记(ReadRequest = 15)01 可变长度的SEQUENCE OF的长度(=1)02 VariableAccessSpecification CHOICE 的(显式)标记(variable-name = 2) 00 '■10 一 可变参照 ObjectName 的编码(variable-name = 0x0010)如请求可接受，该请求的响应为ReadResponse,定义如下： ReadResponse ::= SEQUENCE OF CHOICE {22

// ===== 第 24 页 =====

DL/T 790.6 — 2010 / IEC 61334 - 6: 2000datadata-access-crror}其中：Data::= CHOICE{arraystructure[0] Data[1 ] IMPLICIT dataAcccssError[1] IMPLICIT SEQUENCE OF Data[2] IMPLICIT SEQUENCE OF Dataunsigned [17] IMPLICIT Unsigned8long-unsigned [18] IMPLICIT Unsigned 16,}181包含给定值的 Dummy_NamedVariable 的 ReadResponse PDU 的 A-XDR 编码如下： 0C DLMS PDU CHOICE 的（显式）标记（ReadRespose = 12） 01 ReadRespose 的可变长度 SEQUENCE OF CHOICE 的长度（1）00 CHOICE的（显式）标记（选择的数据项是[0] Data） 02 CHOICE的（显式）标记（选择的数据项是[2] structure） 02 可变长度SEQUENCE OF Data （CHOICE）的长度（结构有2个数据项）11 结构第一数据项的（显式）标记（[⑺Un、igned8）02 Number_O（Counters 的编码值（=2）01 结构第二数据项的（显式）标记（[1], array）02 Array （SEQUENCE OF Data （CHLOICE））的长度（=2, array 中有■两个 Data〉 12 第一Data 的（显式）标记（[18] Unsigned 16） 01 3E Counter_Value_l 的编码值（=0x013E）12 第二 Data 的（显式）标记（[18] Unsignedl6）02 CB Counter_Value_2 的编码值（=Ox02CB）18）此时只需要这些CHOICE型。23

`

// DLT790_6_Sections contains structured sections from the standard
var DLT790_6_Sections = map[string]string{
	"full_content": DLT790_6_Content,
	"extracted_at": "2025-06-28 16:46:45",
	"source_file":  "doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf",
}

// GetDLT790_6_Content returns the full extracted content
func GetDLT790_6_Content() string {
	return DLT790_6_Content
}

// GetDLT790_6_Section returns a specific section if available
func GetDLT790_6_Section(section string) (string, bool) {
	content, exists := DLT790_6_Sections[section]
	return content, exists
}
