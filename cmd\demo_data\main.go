package main

import (
	"encoding/hex"
	"fmt"
	"os"
	"strconv"
	"strings"

	"tp.service/internal/protocol/axdr"
	"tp.service/internal/protocol/dlt69845"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]
	switch command {
	case "encode":
		handleEncode()
	case "decode":
		handleDecode()
	case "demo":
		runDemo()
	default:
		fmt.Printf("未知命令: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("DL/T 698.45 Data 类型演示程序")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  demo_data encode <type> <value>  - 编码指定类型和值")
	fmt.Println("  demo_data decode <hex_data>      - 解码十六进制数据")
	fmt.Println("  demo_data demo                   - 运行演示")
	fmt.Println()
	fmt.Println("支持的类型:")
	fmt.Println("  null                    - NULL 类型")
	fmt.Println("  bool <true|false>       - 布尔类型")
	fmt.Println("  integer <value>         - 8位整数")
	fmt.Println("  long <value>            - 16位整数")
	fmt.Println("  unsigned <value>        - 8位无符号整数")
	fmt.Println("  long-unsigned <value>   - 16位无符号整数")
	fmt.Println("  double-long <value>     - 32位整数")
	fmt.Println("  long64 <value>          - 64位整数")
	fmt.Println("  visible-string <value>  - ASCII字符串")
	fmt.Println("  utf8-string <value>     - UTF-8字符串")
	fmt.Println("  octet-string <hex>      - 字节串（十六进制）")
	fmt.Println("  enum <value>            - 枚举")
	fmt.Println("  oi <value>              - 对象标识符")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  demo_data encode bool true")
	fmt.Println("  demo_data encode integer 123")
	fmt.Println("  demo_data encode visible-string \"Hello\"")
	fmt.Println("  demo_data decode \"03FF\"")
}

func handleEncode() {
	if len(os.Args) < 3 {
		fmt.Println("错误: 缺少类型参数")
		printUsage()
		return
	}

	dataType := os.Args[2]
	var data *dlt69845.Data
	var err error

	switch dataType {
	case "null":
		data = dlt69845.NewDataNull()

	case "bool":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少布尔值")
			return
		}
		value := strings.ToLower(os.Args[3])
		if value == "true" {
			data = dlt69845.NewDataBool(true)
		} else if value == "false" {
			data = dlt69845.NewDataBool(false)
		} else {
			fmt.Printf("错误: 无效的布尔值 '%s'，应为 true 或 false\n", os.Args[3])
			return
		}

	case "integer":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少整数值")
			return
		}
		value, err := strconv.ParseInt(os.Args[3], 10, 8)
		if err != nil {
			fmt.Printf("错误: 无效的8位整数值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataInteger(int8(value))

	case "long":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少长整数值")
			return
		}
		value, err := strconv.ParseInt(os.Args[3], 10, 16)
		if err != nil {
			fmt.Printf("错误: 无效的16位整数值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataLong(int16(value))

	case "unsigned":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少无符号整数值")
			return
		}
		value, err := strconv.ParseUint(os.Args[3], 10, 8)
		if err != nil {
			fmt.Printf("错误: 无效的8位无符号整数值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataUnsigned(uint8(value))

	case "long-unsigned":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少无符号长整数值")
			return
		}
		value, err := strconv.ParseUint(os.Args[3], 10, 16)
		if err != nil {
			fmt.Printf("错误: 无效的16位无符号整数值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataLongUnsigned(uint16(value))

	case "double-long":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少双长整数值")
			return
		}
		value, err := strconv.ParseInt(os.Args[3], 10, 32)
		if err != nil {
			fmt.Printf("错误: 无效的32位整数值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataDoubleLong(int32(value))

	case "long64":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少64位整数值")
			return
		}
		value, err := strconv.ParseInt(os.Args[3], 10, 64)
		if err != nil {
			fmt.Printf("错误: 无效的64位整数值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataLong64(value)

	case "visible-string", "utf8-string":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少字符串值")
			return
		}
		value := os.Args[3]
		// 移除可能的引号
		if len(value) >= 2 && value[0] == '"' && value[len(value)-1] == '"' {
			value = value[1 : len(value)-1]
		}
		if dataType == "visible-string" {
			data = dlt69845.NewDataVisibleString(value)
		} else {
			data = dlt69845.NewDataUTF8String(value)
		}

	case "octet-string":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少十六进制字节串")
			return
		}
		hexStr := strings.ReplaceAll(os.Args[3], " ", "")
		bytes, err := hex.DecodeString(hexStr)
		if err != nil {
			fmt.Printf("错误: 无效的十六进制字符串 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataOctetString(bytes)

	case "enum":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少枚举值")
			return
		}
		value, err := strconv.ParseUint(os.Args[3], 10, 8)
		if err != nil {
			fmt.Printf("错误: 无效的枚举值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataEnum(uint8(value))

	case "oi":
		if len(os.Args) < 4 {
			fmt.Println("错误: 缺少OI值")
			return
		}
		value, err := strconv.ParseUint(os.Args[3], 10, 16)
		if err != nil {
			fmt.Printf("错误: 无效的OI值 '%s': %v\n", os.Args[3], err)
			return
		}
		data = dlt69845.NewDataOI(uint16(value))

	default:
		fmt.Printf("错误: 不支持的类型 '%s'\n", dataType)
		printUsage()
		return
	}

	// 编码
	codec := axdr.NewCodec()
	encoded, err := codec.Encode(data)
	if err != nil {
		fmt.Printf("编码失败: %v\n", err)
		return
	}

	// 输出结果
	fmt.Printf("类型: %s\n", data.GetTypeName())
	fmt.Printf("标签: %d\n", data.Tag)
	fmt.Printf("编码: %s\n", strings.ToUpper(hex.EncodeToString(encoded)))
	fmt.Printf("长度: %d 字节\n", len(encoded))
}

func handleDecode() {
	if len(os.Args) < 3 {
		fmt.Println("错误: 缺少十六进制数据")
		printUsage()
		return
	}

	hexStr := strings.ReplaceAll(os.Args[2], " ", "")
	encoded, err := hex.DecodeString(hexStr)
	if err != nil {
		fmt.Printf("错误: 无效的十六进制字符串 '%s': %v\n", os.Args[2], err)
		return
	}

	// 解码
	codec := axdr.NewCodec()
	data := dlt69845.NewData()
	consumed, err := codec.Decode(encoded, data)
	if err != nil {
		fmt.Printf("解码失败: %v\n", err)
		return
	}

	// 输出结果
	fmt.Printf("输入数据: %s\n", strings.ToUpper(hex.EncodeToString(encoded)))
	fmt.Printf("类型: %s\n", data.GetTypeName())
	fmt.Printf("标签: %d\n", data.Tag)
	fmt.Printf("消耗字节: %d\n", consumed)

	// 根据类型输出值
	switch {
	case data.IsNull():
		fmt.Println("值: NULL")
	case data.IsBool():
		if value, err := data.GetBool(); err == nil {
			fmt.Printf("值: %t\n", value)
		}
	case data.IsInteger():
		if value, err := data.GetInteger(); err == nil {
			fmt.Printf("值: %d\n", value)
		}
	case data.IsString():
		if value, err := data.GetString(); err == nil {
			fmt.Printf("值: \"%s\"\n", value)
		}
	case data.Tag == dlt69845.DataTypeOctetString:
		if value, err := data.GetOctetString(); err == nil {
			fmt.Printf("值: %s\n", strings.ToUpper(hex.EncodeToString(value)))
		}
	case data.Tag == dlt69845.DataTypeBitString:
		if value, err := data.GetBitString(); err == nil {
			fmt.Printf("值: %v\n", value)
		}
	default:
		fmt.Printf("值: %s\n", data.Value.String())
	}
}

func runDemo() {
	fmt.Println("=== DL/T 698.45 Data 类型演示 ===")
	fmt.Println()

	codec := axdr.NewCodec()

	// 演示各种数据类型
	demos := []struct {
		name string
		data *dlt69845.Data
	}{
		{"NULL", dlt69845.NewDataNull()},
		{"布尔值 true", dlt69845.NewDataBool(true)},
		{"布尔值 false", dlt69845.NewDataBool(false)},
		{"8位整数", dlt69845.NewDataInteger(123)},
		{"16位整数", dlt69845.NewDataLong(-12345)},
		{"8位无符号整数", dlt69845.NewDataUnsigned(255)},
		{"16位无符号整数", dlt69845.NewDataLongUnsigned(65535)},
		{"32位整数", dlt69845.NewDataDoubleLong(-123456)},
		{"64位整数", dlt69845.NewDataLong64(9223372036854775807)},
		{"ASCII字符串", dlt69845.NewDataVisibleString("Hello")},
		{"UTF-8字符串", dlt69845.NewDataUTF8String("世界")},
		{"字节串", dlt69845.NewDataOctetString([]byte{0x01, 0x02, 0x03, 0xFF})},
		{"位串", dlt69845.NewDataBitString([]bool{true, false, true, false, true})},
		{"枚举", dlt69845.NewDataEnum(42)},
		{"对象标识符", dlt69845.NewDataOI(0x4000)},
	}

	for i, demo := range demos {
		fmt.Printf("%d. %s\n", i+1, demo.name)
		fmt.Printf("   类型: %s (标签: %d)\n", demo.data.GetTypeName(), demo.data.Tag)

		// 编码
		encoded, err := codec.Encode(demo.data)
		if err != nil {
			fmt.Printf("   编码失败: %v\n", err)
			continue
		}
		fmt.Printf("   编码: %s (%d 字节)\n", strings.ToUpper(hex.EncodeToString(encoded)), len(encoded))

		// 解码验证
		decoded := dlt69845.NewData()
		consumed, err := codec.Decode(encoded, decoded)
		if err != nil {
			fmt.Printf("   解码失败: %v\n", err)
			continue
		}

		if consumed != len(encoded) {
			fmt.Printf("   警告: 消耗字节数不匹配 (期望: %d, 实际: %d)\n", len(encoded), consumed)
		}

		if decoded.Tag != demo.data.Tag {
			fmt.Printf("   警告: 标签不匹配 (期望: %d, 实际: %d)\n", demo.data.Tag, decoded.Tag)
		}

		fmt.Printf("   解码: %s\n", decoded.String())
		fmt.Println()
	}

	fmt.Println("演示完成！")
}
