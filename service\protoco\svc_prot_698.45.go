package service

import (
	"context"
	"encoding/json"
	"fmt"

	// dlt698 "tp.service/internal/protocol/dlt69845"
	proto "tp.service/service/bean/api/protocol"
)

type DLT69845ProtSvc struct {
	proto.UnimplementedDLT69845ProtSvcServer
	// builder *dlt698.Builder
}

func NewDLT69845ProtSvcServer() *DLT69845ProtSvc {
	return &DLT69845ProtSvc{
		// builder: dlt698.NewBuilder(dlt698.SAddrTypeSingle, dlt698.SAddrLogical0, []byte{0x00, 0x00, 0x00, 0x01}, 0x01),
	}
}

// GetSvcInfo 获取服务信息
func (s *DLT69845ProtSvc) GetSvcInfo(ctx context.Context, req *proto.GetSvcInfoReq) (*proto.GetSvcInfoResp, error) {
	return &proto.GetSvcInfoResp{
		Services: &proto.ProtServiceInfo{
			Name:        "DL/T 698.45",
			Version:     "1.0.0",
			Description: "DL/T 698.45 通信协议服务",
		},
	}, nil
}

// ProtDDO 协议数据域组织
func (s *DLT69845ProtSvc) ProtDDO(ctx context.Context, req *proto.CPDOReq) (*proto.CPDOResp, error) {
	// 解析参数
	var params map[string]interface{}
	if err := json.Unmarshal([]byte(req.Params), &params); err != nil {
		return nil, fmt.Errorf("invalid params format: %v", err)
	}

	// 这里应该实现具体的 DL/T 698.45 数据域组织逻辑
	// 目前返回示例数据
	datadomain := fmt.Sprintf("DLT69845_DATADOMAIN_%v", params)

	return &proto.CPDOResp{
		Datadomain: datadomain,
	}, nil
}

// ProtDDP 协议数据域解析
func (s *DLT69845ProtSvc) ProtDDP(ctx context.Context, req *proto.CPDReq) (*proto.CPDResp, error) {
	// 这里应该实现具体的 DL/T 698.45 数据域解析逻辑
	// 目前返回示例数据
	data := fmt.Sprintf(`{"parsed_data": "%s", "protocol": "DL/T 698.45"}`, req.Datadomain)

	return &proto.CPDResp{
		Data: data,
	}, nil
}

// ProtO 协议组织
func (s *DLT69845ProtSvc) ProtO(ctx context.Context, req *proto.CPOReq) (*proto.CPOResp, error) {
	// 解析参数
	var params map[string]interface{}
	if err := json.Unmarshal([]byte(req.Params), &params); err != nil {
		return nil, fmt.Errorf("invalid params format: %v", err)
	}

	// 这里应该实现具体的 DL/T 698.45 协议组织逻辑
	// 目前返回示例数据
	frame := fmt.Sprintf("68%s16", req.Params) // 简化的帧格式

	return &proto.CPOResp{
		Frame: frame,
	}, nil
}

// ProtP 协议解析
func (s *DLT69845ProtSvc) ProtP(ctx context.Context, req *proto.CPPReq) (*proto.CPPResp, error) {
	// 这里应该实现具体的 DL/T 698.45 协议解析逻辑
	// 目前返回示例数据
	data := fmt.Sprintf(`{"frame": "%s", "protocol": "DL/T 698.45", "parsed": true}`, req.Frame)

	return &proto.CPPResp{
		Data: data,
	}, nil
}
